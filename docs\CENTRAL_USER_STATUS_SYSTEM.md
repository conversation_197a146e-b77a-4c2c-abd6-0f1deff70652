# Merkezi Kullanıcı Durumu Kontrol Sistemi

## Genel Bakış

Bu sistem, tüm kullanıcı durumu kontrollerini tek bir merkezi fonksiyonda birleştirerek tutarlı bir mantık akışı sağlar.

## Merkezi Fonksiyon: `checkUserStatus(email)`

Bu fonksiyon, verilen e-posta adresi için kullanıcının durumunu kontrol eder ve aşağıdaki durumlardan birini döner:

### Durum Türleri

1. **`NOT_FOUND`**: Kullanıcı `users` tablosunda bulunamadı
2. **`NEEDS_PASSWORD`**: <PERSON>llanıcı `users` tablosunda var ama `auth.users`'da yok (şifre oluşturması gerekiyor)
3. **`HAS_PASSWORD`**: Kullanıcı hem `users` hem de `auth.users` tablosunda var (normal giriş yapabilir)

## Tutarlı Mantık Akışı

### 1. Users tablosunda VAR + auth.users'da VAR
- **Durum**: `HAS_PASSWORD`
- **Sonuç**: Normal giriş yapabilir
- **Aksiyon**: Login ekranında şifre ile giriş

### 2. Users tablosunda VAR + auth.users'da YOK
- **Durum**: `NEEDS_PASSWORD`
- **Sonuç**: Şifre oluşturma ekranına yönlendirilir
- **Aksiyon**: FirstTimeLogin ekranında şifre belirleme

### 3. Users tablosunda YOK
- **Durum**: `NOT_FOUND`
- **Sonuç**: "Kayıt bulunamadı" hatası
- **Aksiyon**: Kullanıcıya kayıt olmadığı bildirilir

## Kullanılan Fonksiyonlar

### `authService.checkUserStatus(email)`
Merkezi kontrol fonksiyonu. Tüm diğer fonksiyonlar bunu kullanır.

### `authService.checkUserByEmail(email)`
FirstTimeLogin ekranı için. `checkUserStatus`'u kullanarak uygun response döner.

### `authService.signInWithPassword(email, password)`
Login ekranı için. `checkUserStatus`'u kullanarak giriş işlemini yönetir.

## Hata Mesajları

### Login Ekranında
- **NOT_FOUND**: "Bu e-posta adresi sistemde kayıtlı değil."
- **NEEDS_PASSWORD**: "Şifre almak için Hesap Oluştur sayfasına gidin." (needsAccountCreation: true)
- **HAS_PASSWORD + Yanlış Şifre**: "Yanlış şifre. Lütfen tekrar deneyin."

### FirstTimeLogin Ekranında
- **NOT_FOUND**: "Kayıt Bulunamadı - Bu e-posta adresi sistemde kayıtlı değil."
- **HAS_PASSWORD**: "Hesap Zaten Mevcut - Bu e-posta adresi için zaten hesap oluşturulmuş. Lütfen giriş sayfasını kullanın."
- **NEEDS_PASSWORD**: Şifre oluşturma ekranına geçiş

## Avantajlar

1. **Tek Noktadan Kontrol**: Tüm kullanıcı durumu kontrolleri merkezi bir fonksiyonda
2. **Tutarlılık**: Aynı e-posta için her zaman aynı sonuç
3. **Bakım Kolaylığı**: Değişiklikler tek yerden yapılır
4. **Hata Azaltma**: Kod tekrarı ve mantık hatalarını önler
5. **Anlaşılabilirlik**: Net durum tanımları ile kolay anlaşılır

## Güvenlik

- `resetPasswordForEmail` fonksiyonu sadece kullanıcının `auth.users`'da olup olmadığını kontrol etmek için kullanılır
- Gerçek şifre sıfırlama işlemi yapılmaz (dummy redirect URL kullanılır)
- Kullanıcı bilgileri sadece `users` tablosundan alınır

## Gelecek Geliştirmeler

Bu merkezi sistem sayesinde:
- Yeni durumlar kolayca eklenebilir
- Farklı kullanıcı tipleri için özel mantık eklenebilir
- Audit logging kolayca entegre edilebilir
- Cache mekanizması eklenebilir