import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User } from '../../services/authService';
import { useTranslation } from '../../i18n/useTranslation';
import { emailService } from '../../services/emailService';

interface MyCasesScreenProps {
  user: User;
  onBack: () => void;
  onNavigate?: (screen: string, caseItem?: any) => void;
}

const MyCasesScreen = ({ user, onBack, onNavigate }: MyCasesScreenProps) => {
  const { t } = useTranslation();
  const [selectedFilter, setSelectedFilter] = useState<'ALL' | 'ACTIVE' | 'COMPLETED'>('ALL');

  // Mock data - gerçek uygulamada API'den gelecek
  const cases = [
    {
      id: '550e8400-e29b-41d4-a716-446655440001',
      deceased_name: '<PERSON><PERSON>',
      family_contact: '<PERSON><PERSON><PERSON>',
      family_phone: '+90 532 123 45 67',
      family_email: '<EMAIL>', // Test verisi
      status: 'IN_PROGRESS',
      created_at: '2024-01-20',
      priority: 'HIGH',
      progress: 60,
      next_task: 'Belge teslimi',
      location: 'Fatih, İstanbul',
      estimated_completion: '2024-01-21',
      tasks_completed: 3,
      tasks_total: 5,
      burial_type: 'TRADITIONAL',
      death_date: '2024-01-18',
      nationality: 'Türkiye',
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440002',
      deceased_name: 'Fatma Şahin',
      family_contact: 'Ahmet Şahin',
      family_phone: '+90 533 987 65 43',
      family_email: '<EMAIL>', // Test verisi
      status: 'IN_PROGRESS',
      created_at: '2024-01-19',
      priority: 'MEDIUM',
      progress: 40,
      next_task: 'Aile görüşmesi',
      location: 'Üsküdar, İstanbul',
      estimated_completion: '2024-01-22',
      tasks_completed: 2,
      tasks_total: 5,
      burial_type: 'MODERN',
      death_date: '2024-01-17',
      nationality: 'Türkiye',
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440003',
      deceased_name: 'Zeynep Arslan',
      family_contact: 'Mustafa Arslan',
      family_phone: '+90 534 456 78 90',
      family_email: '<EMAIL>', // Test verisi
      status: 'COMPLETED',
      created_at: '2024-01-15',
      priority: 'LOW',
      progress: 100,
      next_task: null,
      location: 'Beşiktaş, İstanbul',
      estimated_completion: '2024-01-18',
      tasks_completed: 4,
      tasks_total: 4,
      burial_type: 'TRADITIONAL',
      death_date: '2024-01-13',
      nationality: 'Türkiye',
    },
  ];

  const statusColors = {
    IN_PROGRESS: '#2ECC71',
    COMPLETED: '#0D3B66',
    PENDING: '#F39C12',
  };

  const statusLabels = {
    IN_PROGRESS: t('tasks.inProgress'),
    COMPLETED: t('tasks.completed'),
    PENDING: t('tasks.pending'),
  };

  const priorityColors = {
    HIGH: '#E74C3C',
    MEDIUM: '#F39C12',
    LOW: '#2ECC71',
  };

  const priorityLabels = {
    HIGH: t('tasks.high'),
    MEDIUM: t('tasks.medium'),
    LOW: t('tasks.low'),
  };

  const filteredCases = cases.filter(caseItem => {
    if (selectedFilter === 'ALL') return true;
    if (selectedFilter === 'ACTIVE') return caseItem.status === 'IN_PROGRESS';
    if (selectedFilter === 'COMPLETED') return caseItem.status === 'COMPLETED';
    return true;
  });

  const handleCallFamily = (phone: string, name: string) => {
    Alert.alert(
      t('common.familyContact'),
      `${name} ile iletişime geçmek istediğinizden emin misiniz?\n\n${phone}`,
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('common.call'), onPress: () => console.log(`Calling ${phone}`) },
      ]
    );
  };

  const handleViewCaseDetails = (caseId: string) => {
    const caseItem = cases.find(c => c.id === caseId);
    if (caseItem && onNavigate) {
      // CaseDetailScreen için uygun format
      const formattedCaseItem = {
        id: caseItem.id,
        status: caseItem.status === 'IN_PROGRESS' ? 'OPEN' : 'CLOSED',
        burial_type: caseItem.burial_type,
        family_name: caseItem.family_contact,
        family_phone: caseItem.family_phone,
        family_email: caseItem.family_email || emailService.getFallbackFamilyEmail(),
        created_at: caseItem.created_at,
        deceased: {
          id: caseId + '_deceased',
          full_name: caseItem.deceased_name,
          date_of_death: caseItem.death_date,
          nationality: caseItem.nationality || 'Türkiye',
        }
      };
      onNavigate('caseDetail', formattedCaseItem);
    } else {
      Alert.alert(t('alerts.caseDetails'), t('alerts.caseDetailsViewing', { id: caseId }));
    }
  };

  const handleViewTasks = (caseId: string) => {
    Alert.alert(t('alerts.caseTasks'), t('alerts.caseTasksViewing', { id: caseId }));
  };

  const handleNavigateToLocation = (location: string) => {
    Alert.alert(t('alerts.navigation'), t('alerts.navigationStarting', { location }));
  };

  const getCaseStats = () => {
    const active = cases.filter(c => c.status === 'IN_PROGRESS').length;
    const completed = cases.filter(c => c.status === 'COMPLETED').length;
    const totalTasks = cases.reduce((sum, c) => sum + c.tasks_total, 0);
    const completedTasks = cases.reduce((sum, c) => sum + c.tasks_completed, 0);

    return { active, completed, totalTasks, completedTasks };
  };

  const stats = getCaseStats();

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#2ECC71" />
        </TouchableOpacity>
        <Text style={styles.title}>{t('navigation.cases')}</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Stats */}
      <View style={styles.statsSection}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.active}</Text>
          <Text style={styles.statLabel}>{t('cases.active')}</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.completed}</Text>
          <Text style={styles.statLabel}>{t('tasks.completed')}</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.completedTasks}/{stats.totalTasks}</Text>
          <Text style={styles.statLabel}>{t('navigation.tasks')}</Text>
        </View>
      </View>

      {/* Filter */}
      <View style={styles.filterSection}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {(['ALL', 'ACTIVE', 'COMPLETED'] as const).map((filter) => (
            <TouchableOpacity
              key={filter}
              style={[
                styles.filterButton,
                selectedFilter === filter && styles.filterButtonActive
              ]}
              onPress={() => setSelectedFilter(filter)}
            >
              <Text style={[
                styles.filterButtonText,
                selectedFilter === filter && styles.filterButtonTextActive
              ]}>
                {filter === 'ALL' ? 'Tümü' :
                 filter === 'ACTIVE' ? 'Aktif' : 'Tamamlanan'}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Cases List */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {filteredCases.map((caseItem) => (
          <TouchableOpacity
            key={caseItem.id}
            style={styles.caseCard}
            onPress={() => handleViewCaseDetails(caseItem.id)}
          >
            <View style={styles.caseHeader}>
              <View style={styles.caseInfo}>
                <Text style={styles.deceasedName}>{caseItem.deceased_name}</Text>
                <Text style={styles.familyContact}>İletişim: {caseItem.family_contact}</Text>
                <View style={styles.caseMeta}>
                  <View style={[
                    styles.statusBadge,
                    { backgroundColor: statusColors[caseItem.status as keyof typeof statusColors] }
                  ]}>
                    <Text style={styles.statusText}>
                      {statusLabels[caseItem.status as keyof typeof statusLabels]}
                    </Text>
                  </View>
                  <View style={[
                    styles.priorityBadge,
                    { backgroundColor: priorityColors[caseItem.priority as keyof typeof priorityColors] }
                  ]}>
                    <Text style={styles.priorityText}>
                      {priorityLabels[caseItem.priority as keyof typeof priorityLabels]}
                    </Text>
                  </View>
                </View>
              </View>
              <View style={styles.caseActions}>
                <Text style={styles.caseDate}>
                  {new Date(caseItem.created_at).toLocaleDateString('tr-TR')}
                </Text>
              </View>
            </View>

            {/* Progress Bar */}
            <View style={styles.progressSection}>
              <View style={styles.progressHeader}>
                <Text style={styles.progressLabel}>İlerleme</Text>
                <Text style={styles.progressPercent}>{caseItem.progress}%</Text>
              </View>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      width: `${caseItem.progress}%`,
                      backgroundColor: statusColors[caseItem.status as keyof typeof statusColors]
                    }
                  ]}
                />
              </View>
              <View style={styles.progressDetails}>
                <Text style={styles.progressText}>
                  {caseItem.tasks_completed}/{caseItem.tasks_total} görev tamamlandı
                </Text>
              </View>
            </View>

            {/* Case Details */}
            <View style={styles.caseDetails}>
              <View style={styles.detailRow}>
                <View style={styles.detailItem}>
                  <Ionicons name="location" size={14} color="#8E9297" />
                  <Text style={styles.detailText}>{caseItem.location}</Text>
                </View>
                <View style={styles.detailItem}>
                  <Ionicons name="calendar" size={14} color="#8E9297" />
                  <Text style={styles.detailText}>
                    {new Date(caseItem.estimated_completion).toLocaleDateString('tr-TR')}
                  </Text>
                </View>
              </View>

              {caseItem.next_task && (
                <View style={styles.nextTaskRow}>
                  <Ionicons name="arrow-forward-circle" size={16} color="#2ECC71" />
                  <Text style={styles.nextTaskText}>Sonraki: {caseItem.next_task}</Text>
                </View>
              )}

              {/* Action Buttons */}
              <View style={styles.actionButtons}>
                <TouchableOpacity
                  style={styles.callButton}
                  onPress={() => handleCallFamily(caseItem.family_phone, caseItem.family_contact)}
                >
                  <Ionicons name="call" size={16} color="#2ECC71" />
                  <Text style={styles.callButtonText}>Aile</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.locationButton}
                  onPress={() => handleNavigateToLocation(caseItem.location)}
                >
                  <Ionicons name="navigate" size={16} color="#F39C12" />
                  <Text style={styles.locationButtonText}>Konum</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.tasksButton}
                  onPress={() => handleViewTasks(caseItem.id)}
                >
                  <Ionicons name="list" size={16} color="#9B59B6" />
                  <Text style={styles.tasksButtonText}>Görevler</Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableOpacity>
        ))}

        {filteredCases.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="folder-outline" size={64} color="#E1E8ED" />
            <Text style={styles.emptyStateTitle}>Vaka bulunamadı</Text>
            <Text style={styles.emptyStateText}>
              {selectedFilter === 'ALL'
                ? 'Henüz size atanmış vaka bulunmuyor.'
                : selectedFilter === 'ACTIVE'
                ? 'Aktif vaka bulunmuyor.'
                : 'Tamamlanmış vaka bulunmuyor.'
              }
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  placeholder: {
    width: 40,
  },
  statsSection: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingVertical: 8,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2ECC71',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 10,
    color: '#8E9297',
  },
  filterSection: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: '#2ECC71',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8E9297',
  },
  filterButtonTextActive: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  caseCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  caseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  caseInfo: {
    flex: 1,
  },
  deceasedName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  familyContact: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 8,
  },
  caseMeta: {
    flexDirection: 'row',
    gap: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  caseActions: {
    alignItems: 'flex-end',
  },
  caseDate: {
    fontSize: 12,
    color: '#8E9297',
  },
  progressSection: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
  },
  progressPercent: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2ECC71',
  },
  progressBar: {
    height: 6,
    backgroundColor: '#E1E8ED',
    borderRadius: 3,
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressDetails: {
    alignItems: 'flex-end',
  },
  progressText: {
    fontSize: 12,
    color: '#8E9297',
  },
  caseDetails: {
    borderTopWidth: 1,
    borderTopColor: '#F5F8FA',
    paddingTop: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  detailText: {
    fontSize: 12,
    color: '#8E9297',
    marginLeft: 6,
  },
  nextTaskRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    backgroundColor: '#F0F9FF',
    padding: 8,
    borderRadius: 8,
  },
  nextTaskText: {
    fontSize: 12,
    color: '#2ECC71',
    fontWeight: '500',
    marginLeft: 6,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  callButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F8FA',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flex: 1,
    borderWidth: 1,
    borderColor: '#2ECC71',
  },
  callButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2ECC71',
    marginLeft: 4,
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F8FA',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flex: 1,
    borderWidth: 1,
    borderColor: '#F39C12',
  },
  locationButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#F39C12',
    marginLeft: 4,
  },
  tasksButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F8FA',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flex: 1,
    borderWidth: 1,
    borderColor: '#9B59B6',
  },
  tasksButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#9B59B6',
    marginLeft: 4,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#8E9297',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#B8C5D1',
    textAlign: 'center',
    paddingHorizontal: 32,
  },
});

export default MyCasesScreen;
