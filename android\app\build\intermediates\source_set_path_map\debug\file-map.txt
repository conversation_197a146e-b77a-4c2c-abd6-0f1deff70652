com.ditib.funeralapp-coordinatorlayout-1.2.0-0 C:\Users\<USER>\.gradle\caches\transforms-3\0031d9e090613e51ce4e364cf3c0d99e\transformed\coordinatorlayout-1.2.0\res
com.ditib.funeralapp-jetified-react-android-0.73.6-debug-1 C:\Users\<USER>\.gradle\caches\transforms-3\043814979a7351adeafae334529a11be\transformed\jetified-react-android-0.73.6-debug\res
com.ditib.funeralapp-sqlite-framework-2.2.0-2 C:\Users\<USER>\.gradle\caches\transforms-3\06c68442365ebdd29936bffb0b86b7a6\transformed\sqlite-framework-2.2.0\res
com.ditib.funeralapp-jetified-emoji2-views-helper-1.2.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\192c14f4613fb0c2b46d35901dd336ef\transformed\jetified-emoji2-views-helper-1.2.0\res
com.ditib.funeralapp-sqlite-2.2.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\24aa34776f3dc0c49dfd0d619c065880\transformed\sqlite-2.2.0\res
com.ditib.funeralapp-cardview-1.0.0-5 C:\Users\<USER>\.gradle\caches\transforms-3\261cca998bad15d377a79210d1f08b1b\transformed\cardview-1.0.0\res
com.ditib.funeralapp-material-1.9.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\284bdced76504d9ec0db1a02932d1b74\transformed\material-1.9.0\res
com.ditib.funeralapp-lifecycle-runtime-2.5.1-7 C:\Users\<USER>\.gradle\caches\transforms-3\44741602003680d6137199a2477b5847\transformed\lifecycle-runtime-2.5.1\res
com.ditib.funeralapp-recyclerview-1.1.0-8 C:\Users\<USER>\.gradle\caches\transforms-3\4c38df224d4593cb021cc10e0f2972f7\transformed\recyclerview-1.1.0\res
com.ditib.funeralapp-jetified-emoji2-1.2.0-9 C:\Users\<USER>\.gradle\caches\transforms-3\4dbb3f58a9fd53a0c4eaa8ffb9d2a519\transformed\jetified-emoji2-1.2.0\res
com.ditib.funeralapp-jetified-tracing-ktx-1.2.0-10 C:\Users\<USER>\.gradle\caches\transforms-3\4fbe5b696242d179f72ce7f20a8695d5\transformed\jetified-tracing-ktx-1.2.0\res
com.ditib.funeralapp-jetified-appcompat-resources-1.6.1-11 C:\Users\<USER>\.gradle\caches\transforms-3\5dcafb075dd6e723143dd09e95479c2e\transformed\jetified-appcompat-resources-1.6.1\res
com.ditib.funeralapp-transition-1.2.0-12 C:\Users\<USER>\.gradle\caches\transforms-3\6346311447512a18de02b94d75a441f3\transformed\transition-1.2.0\res
com.ditib.funeralapp-constraintlayout-2.0.1-13 C:\Users\<USER>\.gradle\caches\transforms-3\63fe6b21dcda9fe2c8f3d9bba0845556\transformed\constraintlayout-2.0.1\res
com.ditib.funeralapp-media-1.0.0-14 C:\Users\<USER>\.gradle\caches\transforms-3\649070c9aa8b5e5f484bbae2322c5e85\transformed\media-1.0.0\res
com.ditib.funeralapp-jetified-activity-1.6.0-15 C:\Users\<USER>\.gradle\caches\transforms-3\6733ab618726902098874d55d8a2628b\transformed\jetified-activity-1.6.0\res
com.ditib.funeralapp-jetified-lifecycle-process-2.4.1-16 C:\Users\<USER>\.gradle\caches\transforms-3\8161456b63a4fc8ccb1191ca896117e0\transformed\jetified-lifecycle-process-2.4.1\res
com.ditib.funeralapp-jetified-drawee-3.1.3-17 C:\Users\<USER>\.gradle\caches\transforms-3\8524324c5ad54824ef34dde0d1c9514d\transformed\jetified-drawee-3.1.3\res
com.ditib.funeralapp-core-1.12.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\97dafb02ce1c76f135d92386cd4ea378\transformed\core-1.12.0\res
com.ditib.funeralapp-drawerlayout-1.1.1-19 C:\Users\<USER>\.gradle\caches\transforms-3\98a66fc804b6660b3e2e1d8a829aaca8\transformed\drawerlayout-1.1.1\res
com.ditib.funeralapp-swiperefreshlayout-1.1.0-20 C:\Users\<USER>\.gradle\caches\transforms-3\9b8d778ccfc3ddb24e5f4fc76a7816a5\transformed\swiperefreshlayout-1.1.0\res
com.ditib.funeralapp-lifecycle-livedata-core-2.5.1-21 C:\Users\<USER>\.gradle\caches\transforms-3\9d9cf1657e8c4dde408c8fa6d2355897\transformed\lifecycle-livedata-core-2.5.1\res
com.ditib.funeralapp-jetified-savedstate-1.2.0-22 C:\Users\<USER>\.gradle\caches\transforms-3\9ef936a1622367cca974b233ed1d8443\transformed\jetified-savedstate-1.2.0\res
com.ditib.funeralapp-jetified-autofill-1.1.0-23 C:\Users\<USER>\.gradle\caches\transforms-3\a009ffcf970f3f3cce3851fbd00b05b9\transformed\jetified-autofill-1.1.0\res
com.ditib.funeralapp-jetified-flipper-0.201.0-24 C:\Users\<USER>\.gradle\caches\transforms-3\ab4e4a0c6fa3458e0b972f8603aac561\transformed\jetified-flipper-0.201.0\res
com.ditib.funeralapp-jetified-viewpager2-1.0.0-25 C:\Users\<USER>\.gradle\caches\transforms-3\b5399c2fc9b9e6da892065d1cea8c700\transformed\jetified-viewpager2-1.0.0\res
com.ditib.funeralapp-jetified-core-ktx-1.12.0-26 C:\Users\<USER>\.gradle\caches\transforms-3\b645b2ed69a2e282f593342336891f63\transformed\jetified-core-ktx-1.12.0\res
com.ditib.funeralapp-jetified-annotation-experimental-1.3.0-27 C:\Users\<USER>\.gradle\caches\transforms-3\c299a6d8bc084da5542640a432ab852f\transformed\jetified-annotation-experimental-1.3.0\res
com.ditib.funeralapp-lifecycle-viewmodel-2.5.1-28 C:\Users\<USER>\.gradle\caches\transforms-3\e560f34a26391fb2e61a3a7065e6f605\transformed\lifecycle-viewmodel-2.5.1\res
com.ditib.funeralapp-jetified-lifecycle-viewmodel-savedstate-2.5.1-29 C:\Users\<USER>\.gradle\caches\transforms-3\ef6ff8cbc1909695aa2014426c70e7d1\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\res
com.ditib.funeralapp-jetified-tracing-1.2.0-30 C:\Users\<USER>\.gradle\caches\transforms-3\f51ca1171a3850aee099841d48f7c123\transformed\jetified-tracing-1.2.0\res
com.ditib.funeralapp-appcompat-1.6.1-31 C:\Users\<USER>\.gradle\caches\transforms-3\f7dbbbc453ac84f8e1f55ee9b7498bc2\transformed\appcompat-1.6.1\res
com.ditib.funeralapp-jetified-startup-runtime-1.1.1-32 C:\Users\<USER>\.gradle\caches\transforms-3\ff11fb567506ca9df4b256047698ea28\transformed\jetified-startup-runtime-1.1.1\res
com.ditib.funeralapp-fragment-1.3.6-33 C:\Users\<USER>\.gradle\caches\transforms-3\ff236f23631a51cc6ee6e7bbd5f16c60\transformed\fragment-1.3.6\res
com.ditib.funeralapp-pngs-34 C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\build\generated\res\pngs\debug
com.ditib.funeralapp-resValues-35 C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\build\generated\res\resValues\debug
com.ditib.funeralapp-packageDebugResources-36 C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.ditib.funeralapp-packageDebugResources-37 C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.ditib.funeralapp-merged_res-38 C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\build\intermediates\merged_res\debug
com.ditib.funeralapp-debug-39 C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\debug\res
com.ditib.funeralapp-main-40 C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\res
com.ditib.funeralapp-packaged_res-41 C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-constants\android\build\intermediates\packaged_res\debug
com.ditib.funeralapp-packaged_res-42 C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-file-system\android\build\intermediates\packaged_res\debug
com.ditib.funeralapp-packaged_res-43 C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-font\android\build\intermediates\packaged_res\debug
com.ditib.funeralapp-packaged_res-44 C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-keep-awake\android\build\intermediates\packaged_res\debug
com.ditib.funeralapp-packaged_res-45 C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-modules-core\android\build\intermediates\packaged_res\debug
com.ditib.funeralapp-packaged_res-46 C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo\android\build\intermediates\packaged_res\debug
com.ditib.funeralapp-packaged_res-47 C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug
com.ditib.funeralapp-packaged_res-48 C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug
