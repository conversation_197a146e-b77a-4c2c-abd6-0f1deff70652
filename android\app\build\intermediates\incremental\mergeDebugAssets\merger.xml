<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-modules-core\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":expo-keep-awake" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-keep-awake\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":expo-font" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-font\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":expo-file-system" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-file-system\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-constants\android\build\intermediates\library_assets\debug\out"><file name="app.config" path="C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-constants\android\build\intermediates\library_assets\debug\out\app.config"/></source></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\react-native-screens\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\build\intermediates\shader_assets\debug\out"/></dataSet></merger>