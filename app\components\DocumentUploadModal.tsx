import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { documentService, DocumentType } from '../services/documentService';
import { useTranslation } from 'react-i18next';

interface DocumentUploadModalProps {
  visible: boolean;
  onClose: () => void;
  caseId: string;
  uploadedBy: string;
  onUploadSuccess: () => void;
}

const DocumentUploadModal: React.FC<DocumentUploadModalProps> = ({
  visible,
  onClose,
  caseId,
  uploadedBy,
  onUploadSuccess,
}) => {
  const { t } = useTranslation();
  const [selectedDocType, setSelectedDocType] = useState<DocumentType | null>(null);
  const [uploading, setUploading] = useState(false);

  const documentTypes: { type: DocumentType; label: string; icon: string }[] = [
    { type: 'DEATH_CERT', label: 'Ölü<PERSON> Belgesi', icon: 'document-text' },
    { type: 'LEICHENPASS', label: 'Leichenpass', icon: 'airplane' },
    { type: 'FORMUL_C', label: 'Formül C', icon: 'document' },
    { type: 'FLIGHT_WAYBILL', label: 'Uçak Kargo Belgesi', icon: 'send' },
    { type: 'PHOTO', label: 'Fotoğraf', icon: 'camera' },
    { type: 'ID_DOCUMENT', label: 'Kimlik Belgesi', icon: 'card' },
    { type: 'MEDICAL_REPORT', label: 'Tıbbi Rapor', icon: 'medical' },
    { type: 'OTHER', label: 'Diğer', icon: 'folder' },
  ];

  const handleDocumentTypeSelect = (docType: DocumentType) => {
    setSelectedDocType(docType);
    showUploadOptions(docType);
  };

  const showUploadOptions = (docType: DocumentType) => {
    Alert.alert(
      'Belge Yükleme',
      'Belgeyi nasıl eklemek istiyorsunuz?',
      [
        {
          text: 'Kamera',
          onPress: () => handleUpload(docType, 'camera'),
        },
        {
          text: 'Galeri',
          onPress: () => handleUpload(docType, 'gallery'),
        },
        {
          text: 'Dosya Seç',
          onPress: () => handleUpload(docType, 'file'),
        },
        {
          text: 'Geri',
          style: 'cancel',
          onPress: () => setSelectedDocType(null),
        },
      ]
    );
  };

  const handleUpload = async (docType: DocumentType, source: 'camera' | 'gallery' | 'file') => {
    setUploading(true);

    try {
      let pickerResponse = null;

      // Kaynak türüne göre dosya seç
      switch (source) {
        case 'camera':
          pickerResponse = await documentService.pickFromCamera();
          break;
        case 'gallery':
          pickerResponse = await documentService.pickFromGallery();
          break;
        case 'file':
          pickerResponse = await documentService.pickDocument();
          break;
      }

      if (!pickerResponse || !pickerResponse.success) {
        if (pickerResponse?.error) {
          Alert.alert(t('common.error'), pickerResponse.error);
        }
        setUploading(false);
        return;
      }

      const pickerResult = pickerResponse.result;
      if (!pickerResult || pickerResult.canceled) {
        setUploading(false);
        return;
      }

      const file = pickerResult.assets[0];

      // Dosya boyutu kontrolü (10MB limit)
      if (file.size && file.size > 10 * 1024 * 1024) {
        Alert.alert(t('common.error'), t('alerts.fileSizeExceeded'));
        setUploading(false);
        return;
      }

      // Storage'a yükle
      const storagePath = await documentService.uploadToStorage(file, caseId, docType);

      if (!storagePath) {
        Alert.alert(t('common.error'), t('alerts.fileUploadFailed'));
        setUploading(false);
        return;
      }

      // Veritabanına kaydet
      const result = await documentService.saveDocument(caseId, docType, storagePath, uploadedBy);

      if (result.success) {
        Alert.alert(
          t('common.success'),
          t('alerts.documentUploadedSuccessfully'),
          [
            {
              text: t('common.ok'),
              onPress: () => {
                onUploadSuccess();
                onClose();
              },
            },
          ]
        );
      } else {
        Alert.alert(t('common.error'), result.error || t('alerts.documentSaveFailed'));
      }
    } catch (error) {
      console.error('DocumentUploadModal: Upload error:', error);
      Alert.alert(t('common.error'), t('alerts.unexpectedError'));
    } finally {
      setUploading(false);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Ionicons name="close" size={24} color="#1C1C1E" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Belge Ekle</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Text style={styles.subtitle}>Eklemek istediğiniz belge türünü seçin:</Text>

          <View style={styles.documentTypes}>
            {documentTypes.map((item) => (
              <TouchableOpacity
                key={item.type}
                style={[
                  styles.documentTypeCard,
                  selectedDocType === item.type && styles.documentTypeCardSelected,
                ]}
                onPress={() => handleDocumentTypeSelect(item.type)}
                disabled={uploading}
              >
                <View style={styles.documentTypeIcon}>
                  <Ionicons
                    name={item.icon as any}
                    size={24}
                    color={selectedDocType === item.type ? '#0D3B66' : '#8E9297'}
                  />
                </View>
                <Text style={[
                  styles.documentTypeLabel,
                  selectedDocType === item.type && styles.documentTypeLabelSelected,
                ]}>
                  {item.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {uploading && (
            <View style={styles.uploadingContainer}>
              <ActivityIndicator size="large" color="#0D3B66" />
              <Text style={styles.uploadingText}>Belge yükleniyor...</Text>
            </View>
          )}

          {/* Cancel Button */}
          {!uploading && (
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelButtonText}>Vazgeç</Text>
            </TouchableOpacity>
          )}
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  closeButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  subtitle: {
    fontSize: 16,
    color: '#1C1C1E',
    marginBottom: 20,
    textAlign: 'center',
  },
  documentTypes: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  documentTypeCard: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  documentTypeCardSelected: {
    borderColor: '#0D3B66',
    backgroundColor: '#F0F4F8',
  },
  documentTypeIcon: {
    marginBottom: 8,
  },
  documentTypeLabel: {
    fontSize: 14,
    color: '#8E9297',
    textAlign: 'center',
    fontWeight: '500',
  },
  documentTypeLabelSelected: {
    color: '#0D3B66',
  },
  uploadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  uploadingText: {
    fontSize: 16,
    color: '#8E9297',
    marginTop: 12,
  },
  cancelButton: {
    marginTop: 30,
    marginBottom: 20,
    padding: 16,
    backgroundColor: '#F5F8FA',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E1E8ED',
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#8E9297',
    fontWeight: '500',
  },
});

export default DocumentUploadModal;
