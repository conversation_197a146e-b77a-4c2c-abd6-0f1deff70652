import { supabase } from '../lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface TaskData {
  id: string;
  case_id: string;
  assignee_id?: string;
  
  // Görev bilgileri
  task_type: 'PICK_UP_FROM_MORGUE' | 'TO_AIRPORT' | 'TO_CONSULATE' | 
             'DELIVERED' | 'DOCUMENT_DELIVERY' | 'FAMILY_MEETING' | 
             'HOSPITAL_TRANSFER';
  title: string;
  description?: string;
  
  // Durum ve öncelik
  status: 'ASSIGNED' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';

  priority: 'URGENT' | 'HIGH' | 'MEDIUM' | 'LOW';
  
  // Zaman bilgileri
  scheduled_at?: string;
  due_at?: string;
  started_at?: string;
  completed_at?: string;
  estimated_duration_minutes?: number;
  
  // Konum ve ilerleme
  location?: string;
  target_latitude?: number;
  target_longitude?: number;
  progress_percentage: number;
  
  // Notlar ve metadata
  notes?: string;
  created_at: string;
  updated_at: string;
  
  // <PERSON>li<PERSON>kili veriler
  case?: {
    id: string;
    deceased_id: string;
    status: string;
    deceased?: {
      full_name: string;
    };
  };
  assigned_to?: {
    id: string;
    full_name: string;
    email: string;
    phone?: string;
  };
}

// Lokasyon hesaplama için yardımcı interface
export interface LocationData {
  latitude: number;
  longitude: number;
}

export interface TaskProgressCalculation {
  taskId: string;
  currentLocation: LocationData;
  targetLocation: LocationData;
  calculatedProgress: number;
  distanceRemaining: number;
  isNearTarget: boolean;
}

// Mesafe hesaplama (Haversine formülü)
function calculateDistance(
  lat1: number, lon1: number, 
  lat2: number, lon2: number
): number {
  const R = 6371; // Dünya'nın yarıçapı (km)
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

// İlerleme yüzdesi hesaplama
function calculateTaskProgress(
  task: TaskData, 
  userLocation: LocationData
): TaskProgressCalculation {
  if (!task.target_latitude || !task.target_longitude) {
    return {
      taskId: task.id,
      currentLocation: userLocation,
      targetLocation: { latitude: 0, longitude: 0 },
      calculatedProgress: task.progress_percentage,
      distanceRemaining: 0,
      isNearTarget: false
    };
  }

  const targetLocation = {
    latitude: task.target_latitude,
    longitude: task.target_longitude
  };

  // Başlangıç konumu (görev başladığında kaydedilecek)
  const startLocation = getTaskStartLocation(task.id) || userLocation;
  
  const totalDistance = calculateDistance(
    startLocation.latitude, startLocation.longitude,
    targetLocation.latitude, targetLocation.longitude
  );
  
  const remainingDistance = calculateDistance(
    userLocation.latitude, userLocation.longitude,
    targetLocation.latitude, targetLocation.longitude
  );
  
  // İlerleme hesaplama (maksimum %90)
  const rawProgress = totalDistance > 0 
    ? ((totalDistance - remainingDistance) / totalDistance) * 90
    : 0;
  
  const calculatedProgress = Math.max(0, Math.min(90, Math.round(rawProgress)));
  
  // Hedefe yakınlık kontrolü (100m içinde)
  const isNearTarget = remainingDistance < 0.1; // 100 metre
  
  return {
    taskId: task.id,
    currentLocation: userLocation,
    targetLocation,
    calculatedProgress: isNearTarget ? 90 : calculatedProgress,
    distanceRemaining: remainingDistance,
    isNearTarget
  };
}

// Görev başlangıç konumunu kaydetme
async function saveTaskStartLocation(taskId: string, location: LocationData): Promise<void> {
  try {
    const startLocations = JSON.parse(
      await AsyncStorage.getItem('taskStartLocations') || '{}'
    );
    startLocations[taskId] = location;
    await AsyncStorage.setItem('taskStartLocations', JSON.stringify(startLocations));
  } catch (error) {
    console.error('Error saving task start location:', error);
  }
}

// Görev başlangıç konumunu getirme
async function getTaskStartLocation(taskId: string): Promise<LocationData | null> {
  try {
    const startLocations = JSON.parse(
      await AsyncStorage.getItem('taskStartLocations') || '{}'
    );
    return startLocations[taskId] || null;
  } catch (error) {
    console.error('Error getting task start location:', error);
    return null;
  }
}

class TaskService {
  async getAllTasks(): Promise<TaskData[]> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select(`
          *,
          case:case_id(
            id,
            deceased_id,
            status,
            deceased:deceased_id(
              full_name
            )
          ),
          assigned_to:assignee_id(
            id,
            full_name,
            email,
            phone
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching tasks:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching tasks:', error);
      return [];
    }
  }

  async getTasksByUserId(userId: string): Promise<TaskData[]> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select(`
          *,
          case:case_id(
            id,
            deceased_id,
            status,
            deceased:deceased_id(
              full_name
            )
          ),
          assigned_to:assignee_id(
            id,
            full_name,
            email,
            phone
          )
        `)
        .eq('assignee_id', userId)
        .order('scheduled_at', { ascending: true });

      if (error) {
        console.error('Error fetching user tasks:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching user tasks:', error);
      return [];
    }
  }

  async getTasksByCaseId(caseId: string): Promise<TaskData[]> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select(`
          *,
          assigned_to:assignee_id (
            id,
            full_name,
            email,
            phone
          )
        `)
        .eq('case_id', caseId)
        .order('scheduled_at', { ascending: true });

      if (error) {
        console.error('Error fetching case tasks:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('TaskService.getTasksByCaseId error:', error);
      throw error;
    }
  }

  async getTaskById(taskId: string): Promise<TaskData | null> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select(`
          *,
          case:case_id(
            id,
            deceased_id,
            status,
            deceased:deceased_id(
              full_name
            )
          ),
          assigned_to:assignee_id(
            id,
            full_name,
            email,
            phone
          )
        `)
        .eq('id', taskId)
        .single();

      if (error) {
        console.error('Error fetching task by ID:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching task by ID:', error);
      return null;
    }
  }

  async updateTaskStatus(
    taskId: string,
    status: TaskData['status'],
    notes?: string
  ): Promise<TaskData | null> {
    try {
      const updateData: any = {
        status
      };

      if (notes) updateData.notes = notes;

      if (status === 'IN_PROGRESS') {
        updateData.started_at = new Date().toISOString();
      } else if (status === 'COMPLETED') {
        updateData.completed_at = new Date().toISOString();
        updateData.progress_percentage = 100; // Manuel tamamlama
      }

      const { data, error } = await supabase
        .from('tasks')
        .update(updateData)
        .eq('id', taskId)
        .select(`
          *,
          case:case_id (
            id,
            deceased_id,
            status,
            deceased:deceased_id (
              full_name
            )
          ),
          assigned_to:assignee_id (
            id,
            full_name,
            email,
            phone
          )
        `)
        .single();

      if (error) {
        console.error('Error updating task status:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error updating task status:', error);
      return null;
    }
  }

  // Konum tabanlı ilerleme güncelleme
  async updateTaskProgress(
    taskId: string, 
    userLocation: LocationData
  ): Promise<TaskData | null> {
    try {
      const task = await this.getTaskById(taskId);
      if (!task) return null;

      const progressCalc = calculateTaskProgress(task, userLocation);
      
      const { data, error } = await supabase
        .from('tasks')
        .update({
          progress_percentage: progressCalc.calculatedProgress,
          updated_at: new Date().toISOString()
        })
        .eq('id', taskId)
        .select(`
          *,
          case:case_id (
            id,
            deceased_id,
            status,
            deceased:deceased_id (
              full_name,
              family_contact_name
            )
          ),
          assigned_to:assignee_id (
            id,
            full_name,
            email,
            phone
          )
        `)
        .single();

      if (error) {
        console.error('Error updating task progress:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error updating task progress:', error);
      return null;
    }
  }

  // Görevi başlatma ve başlangıç konumunu kaydetme
  async startTask(
    taskId: string, 
    userLocation: LocationData
  ): Promise<TaskData | null> {
    try {
      // Başlangıç konumunu kaydet
      await saveTaskStartLocation(taskId, userLocation);
      
      // Görev durumunu güncelle
      return await this.updateTaskStatus(taskId, 'IN_PROGRESS');
    } catch (error) {
      console.error('Error starting task:', error);
      return null;
    }
  }

  // Görevi tamamlama (manuel %100)
  async completeTask(
    taskId: string,
    notes?: string
  ): Promise<TaskData | null> {
    try {
      return await this.updateTaskStatus(taskId, 'COMPLETED', undefined, notes);
    } catch (error) {
      console.error('Error completing task:', error);
      return null;
    }
  }

  // İlerleme hesaplama fonksiyonu (export)
  calculateTaskProgress(
    task: TaskData, 
    userLocation: LocationData
  ): TaskProgressCalculation {
    return calculateTaskProgress(task, userLocation);
  }

  async assignTask(taskId: string, userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('tasks')
        .update({
          assignee_id: userId
        })
        .eq('id', taskId);

      if (error) {
        console.error('Error assigning task:', error);
        throw error;
      }
    } catch (error) {
      console.error('TaskService.assignTask error:', error);
      throw error;
    }
  }

  async createTask(taskData: Partial<TaskData>): Promise<TaskData | null> {
    try {
      const newTask = {
        ...taskData,
        progress_percentage: 0, // Başlangıçta %0
        status: taskData.status || 'ASSIGNED',
        priority: taskData.priority || 'MEDIUM',
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('tasks')
        .insert(newTask)
        .select(`
          *,
          case:case_id(
            id,
            deceased_id,
            status,
            deceased:deceased_id(
              full_name
            )
          ),
          assigned_to:assignee_id(
            id,
            full_name,
            email,
            phone
          )
        `)
        .single();

      if (error) {
        console.error('Error creating task:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error creating task:', error);
      return null;
    }
  }

  async deleteTask(taskId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', taskId);

      if (error) {
        console.error('Error deleting task:', error);
        throw error;
      }
    } catch (error) {
      console.error('TaskService.deleteTask error:', error);
      throw error;
    }
  }

  async getUrgentTasks(limit: number = 10): Promise<TaskData[]> {
    try {
      console.log('TaskService: Fetching urgent tasks (simplified)...');

      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .in('status', ['ASSIGNED', 'IN_PROGRESS'])
        .order('scheduled_at', { ascending: true })
        .limit(limit);

      if (error) {
        console.error('Error fetching urgent tasks:', error);
        return []; // Return empty array instead of throwing
      }

      console.log('TaskService: Successfully fetched urgent tasks:', data?.length || 0);
      return data || [];
    } catch (error) {
      console.error('TaskService.getUrgentTasks error:', error);
      return []; // Return empty array instead of throwing
    }
  }
}

export const taskService = new TaskService();
