import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Path, Rect } from 'react-native-svg';

interface ZSULogoProps {
  width?: number;
  height?: number;
  style?: any;
}

const ZSULogo: React.FC<ZSULogoProps> = ({ 
  width = 120, 
  height = 40, 
  style 
}) => {
  return (
    <View style={[styles.container, style]}>
      <Svg width={width} height={height} viewBox="0 0 300 100">
        {/* Red Z background */}
        <Path
          d="M0 10 C0 4.5 4.5 0 10 0 L70 0 C75.5 0 80 4.5 80 10 L80 70 C80 75.5 75.5 80 70 80 L10 80 C4.5 80 0 75.5 0 70 Z"
          fill="#DC2626"
        />
        
        {/* White Z letter */}
        <Path
          d="M15 20 L60 20 L60 30 L30 50 L60 50 L60 60 L15 60 L15 50 L45 30 L15 30 Z"
          fill="white"
        />
        
        {/* Gray S */}
        <Path
          d="M100 15 L140 15 C150 15 155 20 155 25 C155 30 150 35 140 35 L120 35 C110 35 105 40 105 45 C105 50 110 55 120 55 L160 55 L160 65 L120 65 C105 65 95 55 95 45 C95 35 105 25 120 25 L140 25 C145 25 145 20 140 20 L100 20 Z"
          fill="#6B7280"
        />
        
        {/* Gray U */}
        <Path
          d="M180 15 L190 15 L190 45 C190 55 200 65 210 65 C220 65 230 55 230 45 L230 15 L240 15 L240 45 C240 60 225 75 210 75 C195 75 180 60 180 45 Z"
          fill="#6B7280"
        />
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default ZSULogo;
