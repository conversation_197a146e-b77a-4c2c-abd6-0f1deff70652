{"logs": [{"outputFile": "com.ditib.funeralapp-mergeDebugResources-36:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\284bdced76504d9ec0db1a02932d1b74\\transformed\\material-1.9.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,369,462,545,646,738,842,959,1040,1106,1197,1263,1324,1414,1478,1545,1606,1675,1737,1791,1898,1957,2018,2072,2146,2266,2351,2435,2570,2641,2711,2798,2856,2912,2978,3051,3131,3226,3295,3371,3451,3520,3615,3698,3788,3883,3957,4031,4124,4178,4245,4331,4416,4478,4542,4605,4707,4812,4905,5011,5073,5133", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,97,92,82,100,91,103,116,80,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,83,134,70,69,86,57,55,65,72,79,94,68,75,79,68,94,82,89,94,73,73,92,53,66,85,84,61,63,62,101,104,92,105,61,59,79", "endOffsets": "266,364,457,540,641,733,837,954,1035,1101,1192,1258,1319,1409,1473,1540,1601,1670,1732,1786,1893,1952,2013,2067,2141,2261,2346,2430,2565,2636,2706,2793,2851,2907,2973,3046,3126,3221,3290,3366,3446,3515,3610,3693,3783,3878,3952,4026,4119,4173,4240,4326,4411,4473,4537,4600,4702,4807,4900,5006,5068,5128,5208"}, "to": {"startLines": "2,34,35,36,37,38,48,49,50,52,53,55,58,60,61,62,63,64,65,66,67,68,69,70,71,72,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3124,3222,3315,3398,3499,4510,4614,4731,4889,4955,5118,5332,5461,5551,5615,5682,5743,5812,5874,5928,6035,6094,6155,6209,6283,6621,6706,6790,6925,6996,7066,7153,7211,7267,7333,7406,7486,7581,7650,7726,7806,7875,7970,8053,8143,8238,8312,8386,8479,8533,8600,8686,8771,8833,8897,8960,9062,9167,9260,9366,9428,9488", "endLines": "5,34,35,36,37,38,48,49,50,52,53,55,58,60,61,62,63,64,65,66,67,68,69,70,71,72,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112", "endColumns": "12,97,92,82,100,91,103,116,80,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,83,134,70,69,86,57,55,65,72,79,94,68,75,79,68,94,82,89,94,73,73,92,53,66,85,84,61,63,62,101,104,92,105,61,59,79", "endOffsets": "316,3217,3310,3393,3494,3586,4609,4726,4807,4950,5041,5179,5388,5546,5610,5677,5738,5807,5869,5923,6030,6089,6150,6204,6278,6398,6701,6785,6920,6991,7061,7148,7206,7262,7328,7401,7481,7576,7645,7721,7801,7870,7965,8048,8138,8233,8307,8381,8474,8528,8595,8681,8766,8828,8892,8955,9057,9162,9255,9361,9423,9483,9563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dbbbc453ac84f8e1f55ee9b7498bc2\\transformed\\appcompat-1.6.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,535,641,730,835,956,1039,1121,1212,1305,1399,1493,1593,1686,1781,1875,1966,2057,2143,2253,2357,2460,2568,2676,2781,2946,9880", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "424,530,636,725,830,951,1034,1116,1207,1300,1394,1488,1588,1681,1776,1870,1961,2052,2138,2248,2352,2455,2563,2671,2776,2941,3046,9962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\97dafb02ce1c76f135d92386cd4ea378\\transformed\\core-1.12.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "39,40,41,42,43,44,45,126", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3591,3690,3792,3894,3997,4098,4200,10618", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "3685,3787,3889,3992,4093,4195,4315,10714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\043814979a7351adeafae334529a11be\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,234,318,395,467,535,615,683,750,824,901,984,1064,1134,1213,1293,1368,1456,1543,1618,1694,1769,1864,1940,2017,2087", "endColumns": "72,105,83,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "123,229,313,390,462,530,610,678,745,819,896,979,1059,1129,1208,1288,1363,1451,1538,1613,1689,1764,1859,1935,2012,2082,2155"}, "to": {"startLines": "33,46,47,51,54,56,57,59,73,74,75,113,114,115,116,118,119,120,121,122,123,124,125,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3051,4320,4426,4812,5046,5184,5252,5393,6403,6470,6544,9568,9651,9731,9801,9967,10047,10122,10210,10297,10372,10448,10523,10719,10795,10872,10942", "endColumns": "72,105,83,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "3119,4421,4505,4884,5113,5247,5327,5456,6465,6539,6616,9646,9726,9796,9875,10042,10117,10205,10292,10367,10443,10518,10613,10790,10867,10937,11010"}}]}]}