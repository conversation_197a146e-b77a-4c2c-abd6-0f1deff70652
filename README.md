# DITIB Cenaze Takip Uygulaması (funeral-app)

Bu depo, **DITIB Cenaze Nakli Yardımlaşma Fonu (CNF)** için uçtan‑uca cenaze yönetim platformunun referans implementasyonudur.
Mobil istemci React Native (Expo) ile yazılmıştır; arka uç, Row‑Level‑Security özellikli **Supabase Postgres** + **Edge Functions** üzerinde çalışır. Bildirimler **OneSignal** üzerinden gönderilir.

---
## 🎯 Proje Amaçları
1. **Vefat Bildirimi → Defin** sürecini dijitalleştirmek.
2. Aile yakınlarına 📱 gerçek‑zamanlı takip & push bildirimi sağlamak.
3. DITIB personeline ⚡ otomatik görev atamaları ve denetimli iş akışı sunmak.
4. GDPR / KVKK uyumlu veri saklama ve şeffaf raporlama.

---
## 🚀 Temel Özellikler
| Modül | Açıklama |
|-------|----------|
| **Üyelik & Kimlik Doğrulama** | Supabase e‑posta sihirli link, OAuth veya telefon OTP |
| **Vaka Yönetimi (cases)** | Vefat bildirimi, belge yükleme, durum zaman çizelgesi |
| **Görev Motoru (tasks)** | Edge Function’lar üzerinden otomatik atama & SLA takip |
| **Belge Saklama** | Supabase Storage + imzalı URL, sürüm & yaşam döngüsü yönetimi |
| **Push Bildirimleri** | OneSignal REST ‑ segmentasyon, in‑app mesaj, analytics |
| **Audit & Finans** | Tüm değişiklikler audit_logs’ta; fon masrafları finance_transactions |
| **CI/CD** | GitHub Actions → EAS Build → OTA güncellemeleri |

---
## 🛠️ Kullanılan Teknolojiler
| Katman | Teknoloji |
|--------|-----------|
| Mobil  | Expo SDK 50 · React‑Native · TypeScript · Zustand · TanStack Query |
| Backend| Supabase Postgres 16 · Edge Functions (Deno + TS) · RLS |
| Bildirim| OneSignal (React‑Native SDK + REST) |
| DevOps | GitHub Actions · EAS Build · Docker Compose · Husky/ESLint/Prettier |

---
## 🖼️ Mimarî Genel Bakış
```
RN App ⇄ Supabase Edge Router ⇄ Postgres (RLS)
                    ↳ Edge Functions (create_case, assign_tasks, …)
                    ↳ Storage (documents bucket)
                    ↳ Realtime (WebSocket → OneSignal push)
Logical backups → Off‑site S3

## 📂 Klasör Yapısı (özet)
```
funeral-app/
├── .github/
│   └── workflows/
│       └── expo-eas.yml
├── .env.example                 # ↱ PUBLIC anahtar örnekleri
│                                #   SUPABASE_URL, SUPABASE_ANON_KEY,
│                                #   EXPO_PUBLIC_ONESIGNAL_APP_ID …
├── .gitignore                   # *.env ve backend/supabase/.env satırları var
├── app/
│   ├── app.json
│   ├── index.js
│   ├── constants/
│   │   ├── routes.ts
│   │   ├── enums.ts
│   │   └── env.ts               # ↳ PUBLIC değişken sarmalayıcı
│   ├── theme/
│   │   ├── colors.ts
│   │   └── index.ts
│   ├── i18n/
│   │   ├── tr.json
│   │   ├── de.json
│   │   └── en.json
│   ├── constants/
│   │   ├── routes.ts                    # Stack & tab names
│   │   └── enums.ts                     # CaseStatus, TaskType…
│   ├── utils/
│   │   ├── date.ts                      # formatDate, diffHumanized
│   │   ├── validators.ts                # email, phone, pdf mime check
│   │   └── file.ts                      # pickDocument, uploadWithProgress
│   ├── hooks/
│   │   ├── useAuth.ts
│   │   ├── useCases.ts
│   │   ├── useUpload.ts
│   │   └── usePushNotifications.ts
│   ├── store/
│   │   └── useStore.ts                  # Zustand global state
│   ├── navigation/
│   │   ├── RootNavigator.tsx            # Root stack & bottom tab
│   │   └── linking.ts                   # Deep-linking config
│   ├── services/
│   │   ├── supabaseClient.ts            # createClient + RLS helpers
│   │   ├── authService.ts               # signIn, signOut, sessionRefetch
│   │   ├── caseService.ts               # listCases, createCase, getTimeline
│   │   ├── documentService.ts           # getPresignedUrl, markVerified
│   │   ├── taskService.ts               # startTask, completeTask
│   │   ├── notificationService.ts       # REST call to OneSignal
│   │   └── onesignalConfig.ts           # OneSignal.init(), tagUser…
│   ├── components/
│   │   ├── common/
│   │   │   ├── Button.tsx
│   │   │   ├── Input.tsx
│   │   │   └── Loader.tsx
│   │   ├── case/
│   │   │   ├── CaseHeader.tsx
│   │   │   ├── CaseTimeline.tsx
│   │   │   ├── DocumentUploader.tsx
│   │   │   └── TaskCard.tsx
│   │   └── layout/
│   │       ├── Header.tsx
│   │       └── SafeArea.tsx
│   ├── screens/
│   │   ├── auth/
│   │   │   ├── LoginScreen.tsx
│   │   │   └── SignupScreen.tsx
│   │   ├── case/
│   │   │   ├── CaseListScreen.tsx
│   │   │   ├── CaseDetailScreen.tsx
│   │   │   └── MapScreen.tsx
│   │   ├── profile/
│   │   │   └── ProfileScreen.tsx
│   │   └── settings/
│   │       └── SettingsScreen.tsx
│   ├── tests/
│   │   └── App.test.tsx                 # Example Jest test
│   └── App.tsx
├── backend/
│   └── supabase/
│       ├── .env.example         # ↱ PRIVATE anahtar örnekleri
│       │                        #   SUPABASE_SERVICE_KEY,
│       │                        #   ONESIGNAL_REST_KEY …
│       ├── migrations/
│       ├── functions/
│       ├── storage/
│       └── supabase.config.json
├── scripts/
│   ├── generate-types.sh
│   ├── migrate.sh
│   └── start-dev.sh
├── jest.config.js
├── tsconfig.json
├── package.json
└── README.md
---
## ⚙️ Kurulum

### 1. Depoyu Çek
```bash
git clone https://github.com/ditib/funeral-app.git
cd funeral-app
```

### 2. Çevresel Değişkenler
`.env` dosyasını oluşturun (veya `.env.example`’ı kopyalayın):
```env
SUPABASE_URL=...
SUPABASE_ANON_KEY=...
ONESIGNAL_APP_ID=...
ONESIGNAL_REST_KEY=...
```

### 3. Yerel Supabase + Edge Runner
```bash
docker compose up -d
./scripts/migrate.sh       # şema + RLS
./scripts/generate-types.sh
```

### 4. Mobil Uygulamayı Çalıştır
```bash
pnpm install               # veya yarn / npm i
expo start                 # Expo Dev Server
```

---
## 🔧 Kullanışlı Scriptler
| Komut | Amaç |
|-------|------|
| `pnpm test`         | Jest + React‑Native Testing‑Library |
| `scripts/migrate.sh`| Yeni migration’ları uygulama |
| `scripts/start-dev.sh` | Expo + Supabase CLI aynı anda |
| `scripts/generate-types.sh` | Supabase tiplerini TS’e aktar |

---
## 🏭 CI / CD Akışı
1. **Pull Request**: Lint & test.
2. **Main** birleştiğinde: EAS build (android / ios) + OTA güncelleme.
3. Supabase migrations CI işinde otomatik uygulanır.

---
## 🔐 Şifre Belirleme Sistemi

### Basit Şifre Belirleme (Email Gönderme Olmadan)
Sistem email gönderme gerektirmeden çalışır:

1. **Staff Kontrolü**: Kullanıcı email'i staff tablosunda var mı kontrol edilir
2. **Şifre Durumu**: Auth.users'da kayıt var mı kontrol edilir
3. **Şifre Belirleme**: Yoksa kullanıcı yeni şifre belirleyebilir
4. **Giriş**: Şifre belirlendikten sonra normal giriş yapılabilir

### Kullanım
```typescript
import { authService } from './services/authService';

// Email kontrolü (staff tablosunda var mı?)
const checkResult = await authService.checkStaffByEmail('<EMAIL>');

if (checkResult.success) {
  // Kullanıcı şifre belirleyebilir
  const passwordResult = await authService.createUserWithPassword(
    '<EMAIL>',
    'newPassword123'
  );
}
```

### Admin Paneli
```typescript
import { adminService } from './services/adminService';

// Yeni staff oluştur
const result = await adminService.createUser({
  email: '<EMAIL>',
  full_name: 'Kullanıcı Adı',
  role: 'ADMIN'
});
```

---
## 👥 Rol Tabanlı Yapı

Uygulama, üç farklı kullanıcı rolü için özelleştirilmiş arayüzler sunmaktadır:

### 1. ADMIN Rolü

Yönetici rolü, sistemin tüm yönlerini kontrol etme yetkisine sahiptir:
- **Dashboard**: Genel istatistikler ve sistem durumu
- **Sürücüler**: Sürücü yönetimi
- **Vakalar**: Tüm vakaların yönetimi
- **Görevler**: Görev atama ve takibi
- **Belgeler**: Belge yönetimi ve doğrulama
- **Ayarlar**: Sistem ayarları

Admin kullanıcıları, yeni personel oluşturabilir, vakaları yönetebilir ve tüm sistemi denetleyebilir.

### 2. DRIVER Rolü

Sürücü rolü, cenaze nakil işlemlerini gerçekleştiren personel için tasarlanmıştır:
- **Dashboard**: Kişisel görev özeti
- **Görevlerim**: Atanmış görevlerin listesi ve detayları
- **Vakalarım**: Sorumlu olduğu vakaların listesi
- **Belgeler**: Belge yükleme ve görüntüleme
- **Ayarlar**: Kişisel ayarlar

Sürücüler kendilerine atanan görevleri takip edebilir, belge yükleyebilir ve vaka detaylarını görüntüleyebilir.

### 3. FAMILY Rolü

Aile rolü, vefat eden kişinin yakınları için tasarlanmıştır:
- **Vaka Takibi**: Cenaze işlemlerinin durumunu gerçek zamanlı izleme
- **Belgeler**: Gerekli belgeleri görüntüleme ve yükleme
- **İletişim**: DITIB personeli ile iletişim kurma
- **Bildirimler**: Süreç hakkında güncellemeleri alma

Aile üyeleri, cenaze işlemlerinin her aşamasını takip edebilir ve gerekli belgeleri sisteme yükleyebilir.

---
## 🔒 Güvenlik
- **Row Level Security** tüm tablolarda *ON*, varsayılan `NO PRIVILEGES`.
- JWT süresi: 60 dk access, 24 s refresh.
- Yerel şifreler `.env` dosyasında, prod sırlarda (GitHub → Actions Secrets).
- GDPR uyumlu EU barındırma + OneSignal EU veri merkezi seçeneği.
- **Şifre Belirleme Sistemi**: Email gerektirmeden güvenli şifre belirleme.

---
## 📝 Lisans
[MIT](LICENSE)
