
import React from 'react';

const TaskTimelineScreen = () => {
  return (
    <div className="min-h-screen bg-[#F5F8FA]">
      {/* Header */}
      <div className="bg-white border-b border-[#E1E8ED] px-4 py-4">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-[#8E9297] rounded"></div>
          <div>
            <h1 className="text-lg font-semibold text-[#1C1C1E]">Görevlerim</h1>
            <p className="text-sm text-[#8E9297]">Bugün • 5 aktif görev</p>
          </div>
        </div>
      </div>

      {/* Filter */}
      <div className="bg-white px-4 py-3 border-b border-[#E1E8ED]">
        <div className="flex space-x-2">
          <button className="px-3 py-1 bg-[#0D3B66] text-white rounded-full text-xs font-medium">
            Tümü
          </button>
          <button className="px-3 py-1 bg-[#E1E8ED] text-[#8E9297] rounded-full text-xs font-medium">
            Acil
          </button>
          <button className="px-3 py-1 bg-[#E1E8ED] text-[#8E9297] rounded-full text-xs font-medium">
            Bugün
          </button>
        </div>
      </div>

      {/* Task Cards */}
      <div className="p-4 space-y-4">
        {/* High Priority Task */}
        <div className="bg-white rounded-lg border-l-4 border-l-[#E74C3C] border border-[#E1E8ED] p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <span className="px-2 py-0.5 bg-[#E74C3C] text-white rounded text-xs font-medium">Acil</span>
                <span className="text-xs text-[#8E9297]">Vaka #2024-002</span>
              </div>
              <h3 className="font-medium text-[#1C1C1E] mb-1">Nüfus müdürlüğü işlemleri</h3>
              <p className="text-sm text-[#8E9297]">Vefat belgesinin alınması gerekiyor</p>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="text-xs text-[#8E9297]">Son tarih: Bugün 17:00</div>
            <button className="px-4 py-2 bg-[#0D3B66] text-white rounded-lg text-sm font-medium">
              Başlat
            </button>
          </div>
        </div>

        {/* In Progress Task */}
        <div className="bg-white rounded-lg border-l-4 border-l-[#FAA916] border border-[#E1E8ED] p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <span className="px-2 py-0.5 bg-[#FAA916] text-white rounded text-xs font-medium">Devam Ediyor</span>
                <span className="text-xs text-[#8E9297]">Vaka #2024-001</span>
              </div>
              <h3 className="font-medium text-[#1C1C1E] mb-1">Cenaze evi rezervasyonu</h3>
              <p className="text-sm text-[#8E9297]">Aile ile görüşülerek tarih belirleniyor</p>
            </div>
          </div>
          <div className="mb-3">
            <div className="flex items-center justify-between mb-1">
              <span className="text-xs text-[#8E9297]">İlerleme</span>
              <span className="text-xs text-[#8E9297]">60%</span>
            </div>
            <div className="w-full h-1 bg-[#E1E8ED] rounded-full">
              <div className="w-3/5 h-1 bg-[#FAA916] rounded-full"></div>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="text-xs text-[#8E9297]">Başlangıç: 15:30</div>
            <button className="px-4 py-2 bg-[#2ECC71] text-white rounded-lg text-sm font-medium">
              Tamamla
            </button>
          </div>
        </div>

        {/* Pending Task */}
        <div className="bg-white rounded-lg border border-[#E1E8ED] p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <span className="px-2 py-0.5 bg-[#8E9297] text-white rounded text-xs font-medium">Bekliyor</span>
                <span className="text-xs text-[#8E9297]">Vaka #2024-001</span>
              </div>
              <h3 className="font-medium text-[#1C1C1E] mb-1">Defin işlemleri koordinasyonu</h3>
              <p className="text-sm text-[#8E9297]">Mezarlık ve defin saati planlaması</p>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="text-xs text-[#8E9297]">Planlanan: Yarın 09:00</div>
            <button className="px-4 py-2 bg-[#E1E8ED] text-[#8E9297] rounded-lg text-sm font-medium">
              Bekliyor
            </button>
          </div>
        </div>

        {/* Completed Task */}
        <div className="bg-white rounded-lg border-l-4 border-l-[#2ECC71] border border-[#E1E8ED] p-4 opacity-75">
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <span className="px-2 py-0.5 bg-[#2ECC71] text-white rounded text-xs font-medium">Tamamlandı</span>
                <span className="text-xs text-[#8E9297]">Vaka #2024-001</span>
              </div>
              <h3 className="font-medium text-[#1C1C1E] mb-1">İlk bildirim</h3>
              <p className="text-sm text-[#8E9297]">Vefat bildirimi ve kayıt işlemleri</p>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="text-xs text-[#8E9297]">Tamamlandı: 14:45</div>
            <div className="w-6 h-6 bg-[#2ECC71] rounded-full flex items-center justify-center">
              <div className="w-3 h-2 border-white border-b-2 border-r-2 rotate-45"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskTimelineScreen;
