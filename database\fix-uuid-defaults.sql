-- =============================
-- UUID DEFAULT DEĞERLERİ EKLEME
-- =============================
-- Bu script mevcut tablolara UUID default değerleri ekler
-- Kullanıcı oluşturma hatalarını çözmek için gerekli

-- 1. Users tablosuna UUID default ekle
ALTER TABLE users 
ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- 2. Deceased tablosuna UUID default ekle  
ALTER TABLE deceased 
ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- 3. Cases tablosuna UUID default ekle
ALTER TABLE cases 
ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- 4. Documents tablosuna UUID default ekle
ALTER TABLE documents 
ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- 5. Notifications tablosuna UUID default ekle
ALTER TABLE notifications 
ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- 6. Places tablosuna UUID default ekle
ALTER TABLE places 
ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- 7. Tasks tablosuna UUID default ekle
ALTER TABLE tasks 
ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- Kontrol için - UUID default'ların eklendiğini doğrula
SELECT 
    table_name,
    column_name,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND column_name = 'id' 
    AND data_type = 'uuid'
ORDER BY table_name;

-- Test - Yeni kullanıcı oluşturma
-- INSERT INTO users (role, full_name, email, phone, location, status) 
-- VALUES ('DRIVER', 'Test Sürücü', '<EMAIL>', '+49123456789', 'Berlin', 'ACTIVE');
