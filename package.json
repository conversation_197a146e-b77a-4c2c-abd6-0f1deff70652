{"name": "funeral-app", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@supabase/supabase-js": "^2.30.0", "@tanstack/react-query": "^5.56.2", "babel-plugin-module-resolver": "^5.0.2", "base64-arraybuffer": "^1.0.2", "date-fns": "^3.6.0", "expo": "~53.0.0", "expo-av": "^15.1.4", "expo-document-picker": "~13.1.5", "expo-haptics": "^14.1.4", "expo-image-picker": "~16.1.4", "expo-localization": "^16.1.5", "expo-location": "~18.1.0", "expo-status-bar": "~2.2.3", "i18next": "^25.2.1", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.5.2", "react-native": "0.79.2", "react-native-crypto": "^2.2.0", "react-native-gesture-handler": "~2.18.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.11.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "stream-browserify": "^3.0.0", "vm-browserify": "^1.1.2", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "@types/react-native": "~0.73.0", "jest": "^29.2.1", "jest-expo": "~53.0.5", "typescript": "^5.1.3"}, "private": true}