{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "back": "Back", "next": "Next", "confirm": "Confirm", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "ok": "OK", "call": "Call", "start": "Start", "complete": "Complete", "familyContact": "Family Contact", "change": "Change", "saving": "Saving...", "unknown": "Unknown"}, "navigation": {"home": "Dashboard", "tasks": "My Tasks", "cases": "Cases", "documents": "Documents", "settings": "Settings", "profile": "Profile", "notifications": "Notifications", "help": "Help", "logout": "Logout"}, "dashboard": {"welcome": "Welcome", "todayTasks": "Today's Tasks", "urgentTasks": "Urgent tasks available!", "activeCases": "Active Cases", "recentDocuments": "Recent Documents", "quickActions": "Quick Actions"}, "tasks": {"myTasks": "My Tasks", "newTask": "New Task", "taskDetails": "Task Details", "taskStatus": "Task Status", "pending": "Pending", "inProgress": "In Progress", "completed": "Completed", "priority": "Priority", "high": "High", "medium": "Medium", "low": "Low"}, "cases": {"myCases": "Cases", "newCase": "New Case", "caseDetails": "Case Details", "caseInfo": "Case Information", "caseNotFound": "Case information not found", "allCases": "All Cases", "loadingCases": "Loading cases...", "caseNumber": "Case Number", "caseStatus": "Case Status", "active": "Active", "closed": "Closed", "archived": "Archived"}, "documents": {"myDocuments": "Documents", "newDocument": "New Document", "documentType": "Document Type", "uploadDate": "Upload Date", "fileSize": "File Size", "download": "Download", "upload": "Upload", "takePhoto": "Take Photo", "selectFromGallery": "Select from Gallery", "additionalCopy": "Additional Copy", "specialDocument": "Special Document"}, "settings": {"settings": "Settings", "language": "Language", "notifications": "Notifications", "privacy": "Privacy", "security": "Security", "about": "About", "version": "Version"}, "language": {"selectLanguage": "Select Language", "description": "Select language. Changes will be applied immediately.", "turkish": "Turkish", "english": "English", "german": "German", "languageChanged": "Language changed"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "enterPassword": "Enter your password", "enterPasswordAgain": "Enter your password again", "forgotPassword": "Forgot Password", "rememberMe": "Remember Me", "passwordRequirementsTitle": "Password Requirements", "minSixChars": "At least 8 characters", "atLeastOneLetter": "At least one letter", "atLeastOneNumber": "At least one number", "passwordMismatch": "The passwords you entered do not match. Please make sure to enter the same password in both fields."}, "profile": {"profile": "Profile", "title": "Profile", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "changePassword": "Change Password", "changePasswordFeature": "Password change feature coming soon.", "changePasswordDescription": "Choose a strong password for your security. Your password must be at least 8 characters long and contain both letters and numbers.", "currentPassword": "Current Password", "enterCurrentPassword": "Enter your current password", "enterNewPassword": "Enter your new password", "confirmNewPassword": "Confirm your new password", "updatePassword": "Update Password", "currentPasswordIncorrect": "Current password is incorrect.", "passwordUpdatedSuccessfully": "Your password has been updated successfully.", "passwordUpdateFailed": "An error occurred while updating password.", "firstName": "First Name", "lastName": "Last Name", "fullName": "Full Name", "email": "Email", "phone": "Phone", "role": "Role", "accountInfo": "Account Information", "security": "Security", "application": "Application", "version": "Version", "lastLogin": "Last Login", "user": "User", "notSpecified": "Not specified", "nameRequired": "Name field cannot be empty.", "profileUpdated": "Profile information updated.", "logout": "Logout", "logoutConfirm": "Are you sure you want to logout?"}, "alerts": {"fillAllFields": "Please fill all fields", "connectionError": "Connection Error", "serverConnectionFailed": "Cannot connect to server. Please check your internet connection.", "accountCreationRequired": "Account Creation Required", "accountNotCreated": "You haven't created an account yet.", "createAccount": "Create Account", "enterEmail": "Please enter your email address", "magicLinkSent": "Magic Link <PERSON>t", "checkEmailForMagicLink": "Check your email and click the login link.", "magicLinkFailed": "Magic link could not be sent", "fileTypeNotSupported": "This file type cannot be opened", "fileUrlNotFound": "File URL could not be retrieved", "fileOpenFailed": "File could not be opened", "documentVerified": "Document verified", "documentVerificationFailed": "Document could not be verified", "fileSizeExceeded": "File size cannot exceed 10MB", "fileUploadFailed": "File could not be uploaded", "documentUploadedSuccessfully": "Document uploaded successfully", "documentSaveFailed": "Document could not be saved", "userNotFound": "User Not Found", "passwordResetEmailFailed": "Password reset email could not be sent.", "accountNotFoundCreateFirst": "No account found for this email address. You may need to create an account first.", "checkYourEmail": "Check Your Email", "passwordResetEmailSent": "Password Reset Email <PERSON>", "passwordResetEmailSentMessage": "We sent a password reset link to {{email}}.", "enterEmailForPasswordReset": "Enter your email address and we'll send you a password reset link.", "passwordUpdateFailed": "Password could not be updated. Please try again.", "passwordSetSuccessfully": "Your password has been set successfully. You can now log in with your email and password.", "checkFailed": "Check failed. Please try again.", "enterPassword": "Please enter your password.", "passwordMinLength": "Password must be at least 8 characters.", "accountCreatedSuccessfully": "Your account has been created successfully! You can now log in with your email and password.", "accountCreationFailed": "An error occurred while creating the account.", "unexpectedErrorTryAgain": "An unexpected error occurred. Please try again.", "driversLoadError": "Error occurred while loading drivers", "statusUpdateError": "Error occurred while updating status"}, "invitation": {"title": "Invitation", "welcome": "Welcome!", "role": "Role", "createAccount": "Create Your Account", "welcomeMessage": "Welcome to DITIB system. Create a secure password.", "checkingInvitation": "Checking invitation..."}, "notifications": {"title": "Notifications", "description": "All users receive system notifications. You can only change sound and vibration settings.", "sound": "Notification Sound", "soundDescription": "Play sound for new notifications", "vibration": "Vibration", "vibrationDescription": "Vibrate for new notifications", "infoMessage": "System notifications are received by all users and cannot be turned off. These settings only affect your sound and vibration preferences.", "saveSettings": "Save Settings", "settingsSaved": "Notification settings saved.", "settingsNotSaved": "Settings could not be saved."}, "roles": {"admin": "Administrator", "driver": "Driver", "family": "Family Member"}, "errors": {"invalidEmailFormat": "Email format is not accepted by Supabase. Please try a different email.", "passwordNotSet": "It seems you haven't set a password yet. Please use the 'Create Account' button.", "invalidCredentials": "Email or password is incorrect. Please check and try again.", "emailNotConfirmed": "Your email address is not confirmed. Please contact your administrator.", "tooManyAttempts": "Too many attempts made. Please wait a few minutes.", "loginFailed": "<PERSON><PERSON> failed.", "loginFailedCheckCredentials": "<PERSON><PERSON> failed. Please check your credentials.", "setPassword": "Set Password", "createSecurePassword": "Create a secure password for your account", "newPassword": "New Password"}}