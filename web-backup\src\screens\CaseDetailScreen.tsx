
import React from 'react';

const CaseDetailScreen = () => {
  return (
    <div className="min-h-screen bg-[#F5F8FA]">
      {/* Header */}
      <div className="bg-white border-b border-[#E1E8ED] px-4 py-4">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-[#8E9297] rounded"></div>
          <div>
            <h1 className="text-lg font-semibold text-[#1C1C1E]">Vaka #2024-001</h1>
            <p className="text-sm text-[#8E9297]">Ahmet Yılmaz</p>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Case Info */}
        <div className="bg-white rounded-lg border border-[#E1E8ED] p-4">
          <h2 className="font-medium text-[#1C1C1E] mb-3">Vaka Bilgileri</h2>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-[#8E9297]">Tarih:</span>
              <span className="text-sm text-[#1C1C1E]">15 Aralık 2024, 14:30</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-[#8E9297]">Durum:</span>
              <span className="text-sm text-[#FAA916] font-medium">Devam Ediyor</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-[#8E9297]">Sorumlu:</span>
              <span className="text-sm text-[#1C1C1E]">Ali Veli</span>
            </div>
          </div>
        </div>

        {/* Document Upload */}
        <div className="bg-white rounded-lg border border-[#E1E8ED] p-4">
          <h2 className="font-medium text-[#1C1C1E] mb-3">Belgeler</h2>
          <div className="border-2 border-dashed border-[#E1E8ED] rounded-lg p-6 text-center">
            <div className="w-12 h-12 bg-[#E1E8ED] rounded-lg mx-auto mb-3"></div>
            <p className="text-sm text-[#8E9297] mb-2">Belge yüklemek için tıklayın</p>
            <p className="text-xs text-[#8E9297]">PDF, JPG, PNG (Max 10MB)</p>
          </div>
          
          {/* Uploaded Documents */}
          <div className="mt-4 space-y-2">
            <div className="flex items-center justify-between p-3 bg-[#F5F8FA] rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-[#E74C3C] rounded"></div>
                <div>
                  <p className="text-sm font-medium text-[#1C1C1E]">nufus_cuzdani.pdf</p>
                  <p className="text-xs text-[#8E9297]">2.3 MB</p>
                </div>
              </div>
              <div className="w-6 h-6 bg-[#8E9297] rounded"></div>
            </div>
          </div>
        </div>

        {/* Timeline */}
        <div className="bg-white rounded-lg border border-[#E1E8ED] p-4">
          <h2 className="font-medium text-[#1C1C1E] mb-4">İşlem Süreci</h2>
          
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-[#E1E8ED]"></div>
            
            {/* Timeline Steps */}
            <div className="space-y-6">
              {/* Completed Step */}
              <div className="flex items-start space-x-4">
                <div className="w-3 h-3 bg-[#2ECC71] rounded-full mt-1 relative z-10"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-[#1C1C1E]">Vefat Bildirimi</p>
                  <p className="text-xs text-[#8E9297]">15 Aralık 2024, 14:30</p>
                  <p className="text-xs text-[#2ECC71] mt-1">Tamamlandı</p>
                </div>
              </div>

              {/* Current Step */}
              <div className="flex items-start space-x-4">
                <div className="w-3 h-3 bg-[#FAA916] rounded-full mt-1 relative z-10"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-[#1C1C1E]">Belge Toplama</p>
                  <p className="text-xs text-[#8E9297]">Devam ediyor</p>
                  <p className="text-xs text-[#FAA916] mt-1">İşlemde</p>
                </div>
              </div>

              {/* Pending Steps */}
              <div className="flex items-start space-x-4">
                <div className="w-3 h-3 bg-[#E1E8ED] rounded-full mt-1 relative z-10"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-[#8E9297]">Cenaze Evi Rezervasyon</p>
                  <p className="text-xs text-[#8E9297]">Bekliyor</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-3 h-3 bg-[#E1E8ED] rounded-full mt-1 relative z-10"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-[#8E9297]">Defin İşlemleri</p>
                  <p className="text-xs text-[#8E9297]">Bekliyor</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CaseDetailScreen;
