import { supabase } from '../lib/supabase';

export interface UserData {
  id: string;
  email: string;
  full_name: string;
  role: 'ADMIN' | 'DRIVER' | 'FAMILY';
  status: 'ACTIVE' | 'ON_LEAVE' | 'INACTIVE';
  phone?: string;
  location?: string;
  created_at: string;
  updated_at: string;
  last_login?: string;
}

class UserService {
  async getAllDrivers(): Promise<UserData[]> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('role', 'DRIVER')
        .order('full_name', { ascending: true });

      if (error) {
        console.error('Error fetching drivers:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('UserService.getAllDrivers error:', error);
      throw error;
    }
  }

  async getActiveDrivers(): Promise<UserData[]> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('role', 'DRIVER')
        .eq('status', 'ACTIVE')
        .order('full_name', { ascending: true });

      if (error) {
        console.error('Error fetching active drivers:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('UserService.getActiveDrivers error:', error);
      throw error;
    }
  }

  async getDriverById(driverId: string): Promise<UserData | null> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', driverId)
        .eq('role', 'DRIVER')
        .single();

      if (error) {
        console.error('Error fetching driver:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('UserService.getDriverById error:', error);
      throw error;
    }
  }

  async getUserById(userId: string): Promise<UserData | null> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching user:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('UserService.getUserById error:', error);
      throw error;
    }
  }

  async updateDriverStatus(driverId: string, status: 'ACTIVE' | 'ON_LEAVE' | 'INACTIVE'): Promise<void> {
    try {
      const { error } = await supabase
        .from('users')
        .update({
          status
        })
        .eq('id', driverId)
        .eq('role', 'DRIVER');

      if (error) {
        console.error('Error updating driver status:', error);
        throw error;
      }
    } catch (error) {
      console.error('UserService.updateDriverStatus error:', error);
      throw error;
    }
  }

  async updateDriverInfo(driverId: string, updates: Partial<UserData>): Promise<void> {
    try {
      const { error } = await supabase
        .from('users')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', driverId)
        .eq('role', 'DRIVER');

      if (error) {
        console.error('Error updating driver info:', error);
        throw error;
      }
    } catch (error) {
      console.error('UserService.updateDriverInfo error:', error);
      throw error;
    }
  }

  async searchDrivers(query: string): Promise<UserData[]> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('role', 'DRIVER')
        .or(`full_name.ilike.%${query}%,email.ilike.%${query}%,location.ilike.%${query}%`)
        .order('full_name', { ascending: true });

      if (error) {
        console.error('Error searching drivers:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('UserService.searchDrivers error:', error);
      throw error;
    }
  }

  async getDriverStats(): Promise<{
    total: number;
    active: number;
    on_leave: number;
    inactive: number;
  }> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('status')
        .eq('role', 'DRIVER');

      if (error) {
        console.error('Error fetching driver stats:', error);
        throw error;
      }

      const stats = {
        total: data?.length || 0,
        active: data?.filter(d => d.status === 'ACTIVE').length || 0,
        on_leave: data?.filter(d => d.status === 'ON_LEAVE').length || 0,
        inactive: data?.filter(d => d.status === 'INACTIVE').length || 0,
      };

      return stats;
    } catch (error) {
      console.error('UserService.getDriverStats error:', error);
      throw error;
    }
  }

  async getAllUsers(): Promise<UserData[]> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('full_name', { ascending: true });

      if (error) {
        console.error('Error fetching all users:', error);
        return []; // Return empty array instead of throwing
      }

      return data || [];
    } catch (error) {
      console.error('UserService.getAllUsers error:', error);
      return []; // Return empty array instead of throwing
    }
  }

  async updateLastLogin(userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('users')
        .update({
          last_login: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        console.error('Error updating last login:', error);
        throw error;
      }
    } catch (error) {
      console.error('UserService.updateLastLogin error:', error);
      throw error;
    }
  }
}

export const userService = new UserService();
