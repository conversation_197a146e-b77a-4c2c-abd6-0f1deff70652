
import React, { useState } from 'react';
import LoginScreen from '../screens/LoginScreen';
import CaseListScreen from '../screens/CaseListScreen';
import CaseDetailScreen from '../screens/CaseDetailScreen';
import TaskTimelineScreen from '../screens/TaskTimelineScreen';
import SettingsScreen from '../screens/SettingsScreen';

const Index = () => {
  const [currentScreen, setCurrentScreen] = useState('login');

  const screens = {
    login: <LoginScreen />,
    caseList: <CaseListScreen />,
    caseDetail: <CaseDetailScreen />,
    taskTimeline: <TaskTimelineScreen />,
    settings: <SettingsScreen />
  };

  return (
    <div className="min-h-screen bg-[#F5F8FA]">
      {/* Screen Navigation */}
      <div className="fixed top-0 left-0 right-0 bg-white border-b border-[#E1E8ED] px-4 py-2 z-50">
        <div className="flex items-center justify-between">
          <h1 className="text-sm font-semibold text-[#1C1C1E]">Funeral App - Wireframes</h1>
          <select 
            value={currentScreen} 
            onChange={(e) => setCurrentScreen(e.target.value)}
            className="text-xs border border-[#E1E8ED] rounded px-2 py-1"
          >
            <option value="login">Login Screen</option>
            <option value="caseList">Case List Screen</option>
            <option value="caseDetail">Case Detail Screen</option>
            <option value="taskTimeline">Task Timeline Screen</option>
            <option value="settings">Settings Screen</option>
          </select>
        </div>
      </div>

      {/* Current Screen */}
      <div className="pt-12">
        {screens[currentScreen]}
      </div>
    </div>
  );
};

export default Index;
