import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Platform,
  StatusBar,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User } from '../services/authService';
import { useTranslation } from '../i18n/useTranslation';

// Import screens (will create these)
import DriverDashboardScreen from '../screens/driver/DriverDashboardScreen';
import MyTasksScreen from '../screens/driver/MyTasksScreen';
import MyCasesScreen from '../screens/driver/MyCasesScreen';
import MyDocumentsScreen from '../screens/driver/MyDocumentsScreen';
import NotificationsScreen from '../screens/NotificationsScreen';
import ProfileScreen from '../screens/ProfileScreen';
import LanguageScreen from '../screens/LanguageScreen';
import CaseDetailScreen from '../screens/CaseDetailScreen';

interface DriverNavigatorProps {
  user: User;
  onLogout: () => void;
}

type DriverScreen =
  | 'home'
  | 'dashboard'
  | 'tasks'
  | 'cases'
  | 'caseDetail'
  | 'documents'
  | 'notifications'
  | 'settings'
  | 'language'
  | 'profile';

const DriverNavigator = ({ user, onLogout }: DriverNavigatorProps) => {
  const { t } = useTranslation();
  const [currentScreen, setCurrentScreen] = useState<DriverScreen>('home');
  const [navigationStack, setNavigationStack] = useState<DriverScreen[]>(['home']);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [selectedCaseItem, setSelectedCaseItem] = useState<any>(null);

  // Navigation functions
  const navigateToScreen = (screen: DriverScreen, caseItem?: any) => {
    if (screen === 'settings') {
      setShowSettingsModal(true);
      return;
    }
    if (screen === 'caseDetail' && caseItem) {
      setSelectedCaseItem(caseItem);
    }
    setNavigationStack(prev => [...prev, screen]);
    setCurrentScreen(screen);
  };

  const navigateBack = () => {
    if (navigationStack.length > 1) {
      const newStack = [...navigationStack];
      newStack.pop(); // Remove current screen
      const previousScreen = newStack[newStack.length - 1];
      setNavigationStack(newStack);
      setCurrentScreen(previousScreen);
    }
  };

  const menuItems = [
    { id: 'dashboard', title: t('navigation.home'), icon: 'analytics', color: '#0D3B66', urgent: false, count: 0, badge: null },
    { id: 'tasks', title: t('navigation.tasks'), icon: 'checkmark-circle', color: '#F39C12', urgent: false, count: 0, badge: null },
    { id: 'cases', title: t('navigation.cases'), icon: 'folder-open', color: '#E74C3C', urgent: false, count: 0, badge: null },
    { id: 'documents', title: t('navigation.documents'), icon: 'camera', color: '#9B59B6', urgent: false, count: 0, badge: null },
    { id: 'settings', title: t('navigation.settings'), icon: 'settings', color: '#FAA916', urgent: false, count: 0, badge: null },
  ];

  const renderScreen = () => {
    switch (currentScreen) {
      case 'home':
        return null; // Will show menu grid below
      case 'dashboard':
        return <DriverDashboardScreen user={user} onNavigate={navigateToScreen} onBack={navigateBack} />;
      case 'tasks':
        return <MyTasksScreen user={user} onBack={navigateBack} />;
      case 'cases':
        return <MyCasesScreen user={user} onBack={navigateBack} onNavigate={navigateToScreen} />;
      case 'caseDetail':
        return <CaseDetailScreen route={{ params: { caseItem: selectedCaseItem } }} navigation={{ goBack: navigateBack }} />;
      case 'documents':
        return <MyDocumentsScreen user={user} onBack={navigateBack} />;
      case 'notifications':
        return <NotificationsScreen onBack={navigateBack} />;
      case 'profile':
        return <ProfileScreen user={user} onBack={navigateBack} onLogout={onLogout} />;
      case 'language':
        return <LanguageScreen onBack={navigateBack} />;
      default:
        return null; // Will show menu grid below
    }
  };

  // If not on home, show the selected screen
  if (currentScreen !== 'home') {
    return renderScreen();
  }

  // Home with navigation menu
  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <View style={styles.logoContainer}>
            <Ionicons name="car" size={24} color="#FFFFFF" />
          </View>
          <View>
            <Text style={styles.welcomeText}>Sürücü</Text>
            <Text style={styles.userName}>{user.full_name}</Text>
          </View>
        </View>
        <TouchableOpacity
          style={styles.profileButton}
          onPress={() => navigateToScreen('profile')}
        >
          <Ionicons name="person-circle" size={32} color="#0D3B66" />
        </TouchableOpacity>
      </View>





      {/* Menu Grid */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Text style={styles.sectionTitle}>Günlük İşlemler</Text>

        <View style={styles.menuGrid}>
          {menuItems.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={styles.menuItem}
              onPress={() => navigateToScreen(item.id as DriverScreen)}
            >
              <View style={styles.menuHeader}>
                <View style={[styles.menuIcon, { backgroundColor: item.color }]}>
                  <Ionicons name={item.icon as any} size={24} color="#FFFFFF" />
                </View>
                {(item.urgent || item.count || item.badge) && (
                  <View style={[
                    styles.badge,
                    item.urgent ? styles.urgentBadge : styles.normalBadge
                  ]}>
                    <Text style={styles.badgeText}>
                      {item.urgent || item.count || item.badge}
                    </Text>
                  </View>
                )}
              </View>
              <Text style={styles.menuTitle}>{item.title}</Text>
              {item.urgent && (
                <Text style={styles.urgentText}>{t('dashboard.urgentTasks')}</Text>
              )}
            </TouchableOpacity>
          ))}
        </View>

        {/* Today's Schedule */}
        <View style={styles.todaySchedule}>
          <Text style={styles.sectionTitle}>Bugünkü Program</Text>
          <View style={styles.scheduleItem}>
            <View style={styles.timeSlot}>
              <Text style={styles.timeText}>09:00</Text>
            </View>
            <View style={styles.scheduleContent}>
              <Text style={styles.scheduleTitle}>Cenaze Nakli</Text>
              <Text style={styles.scheduleLocation}>Fatih Camii → Karacaahmet</Text>
            </View>
            <View style={styles.scheduleStatus}>
              <Ionicons name="time" size={16} color="#FAA916" />
            </View>
          </View>

          <View style={styles.scheduleItem}>
            <View style={styles.timeSlot}>
              <Text style={styles.timeText}>14:30</Text>
            </View>
            <View style={styles.scheduleContent}>
              <Text style={styles.scheduleTitle}>Belge Teslimi</Text>
              <Text style={styles.scheduleLocation}>Nüfus Müdürlüğü</Text>
            </View>
            <View style={styles.scheduleStatus}>
              <Ionicons name="checkmark-circle" size={16} color="#2ECC71" />
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Settings Modal */}
      <Modal
        visible={showSettingsModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowSettingsModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          {/* Modal Header */}
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setShowSettingsModal(false)}
              style={styles.modalCloseButton}
            >
              <Ionicons name="close" size={24} color="#8E9297" />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Ayarlar</Text>
            <View style={styles.modalHeaderRight} />
          </View>

          {/* Modal Content */}
          <View style={styles.modalContent}>
            <Text style={styles.modalSectionTitle}>Uygulama Ayarları</Text>

            <View style={styles.settingsOptions}>
              <TouchableOpacity
                style={styles.settingsOption}
                onPress={() => {
                  setShowSettingsModal(false);
                  navigateToScreen('language');
                }}
              >
                <View style={styles.settingsOptionLeft}>
                  <View style={[styles.settingsOptionIcon, { backgroundColor: '#FAA916' }]}>
                    <Ionicons name="language" size={20} color="#FFFFFF" />
                  </View>
                  <View style={styles.settingsOptionInfo}>
                    <Text style={styles.settingsOptionTitle}>Dil</Text>
                    <Text style={styles.settingsOptionDescription}>Uygulama dilini değiştir</Text>
                  </View>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#8E9297" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.settingsOption}
                onPress={() => {
                  setShowSettingsModal(false);
                  navigateToScreen('notifications');
                }}
              >
                <View style={styles.settingsOptionLeft}>
                  <View style={[styles.settingsOptionIcon, { backgroundColor: '#2ECC71' }]}>
                    <Ionicons name="notifications" size={20} color="#FFFFFF" />
                  </View>
                  <View style={styles.settingsOptionInfo}>
                    <Text style={styles.settingsOptionTitle}>Bildirimler</Text>
                    <Text style={styles.settingsOptionDescription}>Bildirim ayarlarını yönet</Text>
                  </View>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#8E9297" />
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
    backgroundColor: '#FFFFFF',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#2ECC71',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  welcomeText: {
    fontSize: 14,
    color: '#8E9297',
  },
  userName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  profileButton: {
    padding: 4,
  },

  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    marginTop: 16,
    gap: 12,
  },
  emergencyButton: {
    flex: 1,
    backgroundColor: '#E74C3C',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
  },
  emergencyText: {
    color: '#FFFFFF',
    fontWeight: '600',
    marginLeft: 8,
    fontSize: 14,
  },
  locationButton: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#0D3B66',
  },
  locationText: {
    color: '#0D3B66',
    fontWeight: '600',
    marginLeft: 8,
    fontSize: 14,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginTop: 24,
    marginBottom: 16,
  },
  menuGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  menuItem: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  menuHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  menuIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  urgentBadge: {
    backgroundColor: '#E74C3C',
  },
  normalBadge: {
    backgroundColor: '#FAA916',
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  menuTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  urgentText: {
    fontSize: 12,
    color: '#E74C3C',
    fontWeight: '500',
  },
  todaySchedule: {
    marginTop: 8,
    marginBottom: 32,
  },
  scheduleItem: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E1E8ED',
    alignItems: 'center',
  },
  timeSlot: {
    width: 60,
    alignItems: 'center',
  },
  timeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0D3B66',
  },
  scheduleContent: {
    flex: 1,
    marginLeft: 16,
  },
  scheduleTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  scheduleLocation: {
    fontSize: 12,
    color: '#8E9297',
  },
  scheduleStatus: {
    marginLeft: 12,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  modalCloseButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  modalHeaderRight: {
    width: 32,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 24,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 16,
  },
  settingsOptions: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  settingsOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  settingsOptionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingsOptionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingsOptionInfo: {
    flex: 1,
  },
  settingsOptionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  settingsOptionDescription: {
    fontSize: 14,
    color: '#8E9297',
  },
});

export default DriverNavigator;
