{"logs": [{"outputFile": "com.ditib.funeralapp-mergeDebugResources-36:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dbbbc453ac84f8e1f55ee9b7498bc2\\transformed\\appcompat-1.6.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "418,525,627,737,823,928,1045,1123,1199,1290,1383,1478,1572,1666,1759,1854,1951,2042,2133,2217,2321,2433,2532,2638,2749,2851,3014,9933", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "520,622,732,818,923,1040,1118,1194,1285,1378,1473,1567,1661,1754,1849,1946,2037,2128,2212,2316,2428,2527,2633,2744,2846,3009,3107,10011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\043814979a7351adeafae334529a11be\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,229,318,401,472,543,630,698,768,847,929,1015,1101,1171,1247,1324,1406,1487,1569,1644,1715,1785,1869,1942,2020,2091", "endColumns": "71,101,88,82,70,70,86,67,69,78,81,85,85,69,75,76,81,80,81,74,70,69,83,72,77,70,79", "endOffsets": "122,224,313,396,467,538,625,693,763,842,924,1010,1096,1166,1242,1319,1401,1482,1564,1639,1710,1780,1864,1937,2015,2086,2166"}, "to": {"startLines": "35,48,49,53,56,58,59,61,75,76,77,115,116,117,118,120,121,122,123,124,125,126,127,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3112,4343,4445,4833,5080,5227,5298,5446,6457,6527,6606,9615,9701,9787,9857,10016,10093,10175,10256,10338,10413,10484,10554,10739,10812,10890,10961", "endColumns": "71,101,88,82,70,70,86,67,69,78,81,85,85,69,75,76,81,80,81,74,70,69,83,72,77,70,79", "endOffsets": "3179,4440,4529,4911,5146,5293,5380,5509,6522,6601,6683,9696,9782,9852,9928,10088,10170,10251,10333,10408,10479,10549,10633,10807,10885,10956,11036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\97dafb02ce1c76f135d92386cd4ea378\\transformed\\core-1.12.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "41,42,43,44,45,46,47,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3612,3710,3812,3913,4012,4117,4224,10638", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "3705,3807,3908,4007,4112,4219,4338,10734"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\284bdced76504d9ec0db1a02932d1b74\\transformed\\material-1.9.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,368,446,524,601,704,796,888,1014,1095,1160,1259,1335,1396,1485,1549,1616,1670,1738,1798,1852,1969,2029,2091,2145,2217,2339,2423,2515,2652,2730,2812,2900,2954,3005,3071,3143,3220,3304,3376,3453,3527,3598,3686,3757,3850,3945,4019,4093,4189,4241,4308,4394,4482,4544,4608,4671,4781,4877,4976,5074,5132,5187", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,87,53,50,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,57,54,78", "endOffsets": "363,441,519,596,699,791,883,1009,1090,1155,1254,1330,1391,1480,1544,1611,1665,1733,1793,1847,1964,2024,2086,2140,2212,2334,2418,2510,2647,2725,2807,2895,2949,3000,3066,3138,3215,3299,3371,3448,3522,3593,3681,3752,3845,3940,4014,4088,4184,4236,4303,4389,4477,4539,4603,4666,4776,4872,4971,5069,5127,5182,5261"}, "to": {"startLines": "2,36,37,38,39,40,50,51,52,54,55,57,60,62,63,64,65,66,67,68,69,70,71,72,73,74,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3184,3262,3340,3417,3520,4534,4626,4752,4916,4981,5151,5385,5514,5603,5667,5734,5788,5856,5916,5970,6087,6147,6209,6263,6335,6688,6772,6864,7001,7079,7161,7249,7303,7354,7420,7492,7569,7653,7725,7802,7876,7947,8035,8106,8199,8294,8368,8442,8538,8590,8657,8743,8831,8893,8957,9020,9130,9226,9325,9423,9481,9536", "endLines": "7,36,37,38,39,40,50,51,52,54,55,57,60,62,63,64,65,66,67,68,69,70,71,72,73,74,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,87,53,50,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,57,54,78", "endOffsets": "413,3257,3335,3412,3515,3607,4621,4747,4828,4976,5075,5222,5441,5598,5662,5729,5783,5851,5911,5965,6082,6142,6204,6258,6330,6452,6767,6859,6996,7074,7156,7244,7298,7349,7415,7487,7564,7648,7720,7797,7871,7942,8030,8101,8194,8289,8363,8437,8533,8585,8652,8738,8826,8888,8952,9015,9125,9221,9320,9418,9476,9531,9610"}}]}]}