import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User } from '../../services/authService';
import { useAlertHelpers } from '../../hooks/useAlert';
import ScreenLayout from '../../components/ScreenLayout';
import { useTranslation } from 'react-i18next';

interface DriverDashboardScreenProps {
  user: User;
  onNavigate: (screen: string) => void;
  onBack?: () => void;
}

const DriverDashboardScreen = ({ user, onNavigate, onBack }: DriverDashboardScreenProps) => {
  const { t } = useTranslation();
  const { showConfirm, showInfo } = useAlertHelpers();
  const todayStats = [
    { title: t('dashboard.todayTasks'), value: '5', color: '#F39C12', icon: 'checkmark-circle' },
    { title: t('tasks.completed'), value: '3', color: '#2ECC71', icon: 'checkmark-done' },
    { title: t('tasks.pending'), value: '2', color: '#E74C3C', icon: 'time' },
    { title: 'Toplam Mesafe', value: '45km', color: '#9B59B6', icon: 'car' },
  ];

  const todaySchedule = [
    {
      id: 1,
      time: '09:00',
      title: 'Cenaze Nakli',
      location: 'Fatih Camii → Karacaahmet',
      status: 'COMPLETED',
      priority: 'HIGH',
      estimatedDuration: '2 saat',
    },
    {
      id: 2,
      time: '11:30',
      title: 'Belge Teslimi',
      location: 'Nüfus Müdürlüğü',
      status: 'COMPLETED',
      priority: 'MEDIUM',
      estimatedDuration: '1 saat',
    },
    {
      id: 3,
      time: '14:00',
      title: 'Aile Ziyareti',
      location: 'Üsküdar - Ev Adresi',
      status: 'IN_PROGRESS',
      priority: 'HIGH',
      estimatedDuration: '1.5 saat',
    },
    {
      id: 4,
      time: '16:30',
      title: 'Hastane Nakli',
      location: 'Şişli Etfal → Morgue',
      status: 'PENDING',
      priority: 'HIGH',
      estimatedDuration: '2 saat',
    },
  ];

  const urgentTasks = [
    {
      id: 1,
      title: 'Acil Cenaze Nakli',
      case: 'Ahmet Yılmaz Vakası',
      time: '30 dakika içinde',
      priority: 'URGENT',
    },
    {
      id: 2,
      title: 'Belge Onayı Bekleniyor',
      case: 'Fatma Şahin Vakası',
      time: '1 saat içinde',
      priority: 'HIGH',
    },
  ];



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return '#2ECC71';
      case 'IN_PROGRESS': return '#F39C12';
      case 'PENDING': return '#E74C3C';
      default: return '#8E9297';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'COMPLETED': return t('tasks.completed');
      case 'IN_PROGRESS': return t('tasks.inProgress');
      case 'PENDING': return t('tasks.pending');
      default: return status;
    }
  };

  return (
    <ScreenLayout
      title={t('navigation.home')}
      showBackButton={!!onBack}
      onBack={onBack}
      scrollable
      contentPadding={false}
    >


        {/* Today Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('dashboard.todayTasks')}</Text>
          <View style={styles.statsGrid}>
            {todayStats.map((stat, index) => (
              <View key={index} style={styles.statCard}>
                <View style={[styles.statIcon, { backgroundColor: stat.color }]}>
                  <Ionicons name={stat.icon as any} size={18} color="#FFFFFF" />
                </View>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statTitle}>{stat.title}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Urgent Tasks */}
        {urgentTasks.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Acil Görevler</Text>
              <TouchableOpacity onPress={() => onNavigate('tasks')}>
                <Text style={styles.seeAllText}>Tümünü Gör</Text>
              </TouchableOpacity>
            </View>
            {urgentTasks.map((task) => (
              <View key={task.id} style={styles.urgentTaskCard}>
                <View style={styles.urgentTaskHeader}>
                  <Ionicons name="warning" size={20} color="#E74C3C" />
                  <Text style={styles.urgentTaskTitle}>{task.title}</Text>
                </View>
                <Text style={styles.urgentTaskCase}>{task.case}</Text>
                <Text style={styles.urgentTaskTime}>{task.time}</Text>
              </View>
            ))}
          </View>
        )}

        {/* Today's Schedule */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Bugünkü Program</Text>
            <TouchableOpacity onPress={() => onNavigate('tasks')}>
              <Text style={styles.seeAllText}>Detay</Text>
            </TouchableOpacity>
          </View>
          {todaySchedule.map((item) => (
            <View key={item.id} style={styles.scheduleCard}>
              <View style={styles.scheduleTime}>
                <Text style={styles.timeText}>{item.time}</Text>
                <View style={[
                  styles.statusDot,
                  { backgroundColor: getStatusColor(item.status) }
                ]} />
              </View>
              <View style={styles.scheduleContent}>
                <View style={styles.scheduleHeader}>
                  <Text style={styles.scheduleTitle}>{item.title}</Text>
                  <View style={[
                    styles.statusBadge,
                    { backgroundColor: getStatusColor(item.status) }
                  ]}>
                    <Text style={styles.statusText}>
                      {getStatusLabel(item.status)}
                    </Text>
                  </View>
                </View>
                <Text style={styles.scheduleLocation}>{item.location}</Text>
                <View style={styles.scheduleFooter}>
                  <View style={styles.scheduleDetail}>
                    <Ionicons name="time" size={12} color="#8E9297" />
                    <Text style={styles.scheduleDetailText}>{item.estimatedDuration}</Text>
                  </View>
                  <View style={[
                    styles.priorityBadge,
                    { backgroundColor: item.priority === 'HIGH' ? '#E74C3C' : '#F39C12' }
                  ]}>
                    <Text style={styles.priorityText}>
                      {item.priority === 'HIGH' ? t('tasks.high') : t('tasks.medium')}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          ))}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Hızlı İşlemler</Text>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => onNavigate('tasks')}
            >
              <Ionicons name="list" size={24} color="#F39C12" />
              <Text style={styles.quickActionText}>Görevlerim</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => onNavigate('cases')}
            >
              <Ionicons name="folder" size={24} color="#E74C3C" />
              <Text style={styles.quickActionText}>{t('navigation.cases')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => onNavigate('documents')}
            >
              <Ionicons name="camera" size={24} color="#9B59B6" />
              <Text style={styles.quickActionText}>Belge Ekle</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => onNavigate('notifications')}
            >
              <Ionicons name="notifications" size={24} color="#2ECC71" />
              <Text style={styles.quickActionText}>Bildirimler</Text>
            </TouchableOpacity>
          </View>
        </View>
    </ScreenLayout>
  );
};

const styles = StyleSheet.create({
  emergencySection: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    marginTop: 16,
    gap: 12,
  },
  emergencyButton: {
    flex: 1,
    backgroundColor: '#E74C3C',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  emergencyText: {
    color: '#FFFFFF',
    fontWeight: '700',
    marginLeft: 8,
    fontSize: 14,
  },
  locationButton: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#2ECC71',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  locationText: {
    color: '#2ECC71',
    fontWeight: '700',
    marginLeft: 8,
    fontSize: 14,
  },
  section: {
    paddingHorizontal: 24,
    marginTop: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  seeAllText: {
    fontSize: 14,
    color: '#2ECC71',
    fontWeight: '500',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  statCard: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  statIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  statTitle: {
    fontSize: 11,
    color: '#8E9297',
    textAlign: 'center',
  },
  urgentTaskCard: {
    backgroundColor: '#FFF5F5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#E74C3C',
  },
  urgentTaskHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  urgentTaskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginLeft: 8,
  },
  urgentTaskCase: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 4,
  },
  urgentTaskTime: {
    fontSize: 12,
    color: '#E74C3C',
    fontWeight: '600',
  },
  scheduleCard: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  scheduleTime: {
    alignItems: 'center',
    marginRight: 16,
    minWidth: 60,
  },
  timeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2ECC71',
    marginBottom: 8,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  scheduleContent: {
    flex: 1,
  },
  scheduleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  scheduleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  scheduleLocation: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 8,
  },
  scheduleFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  scheduleDetail: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scheduleDetailText: {
    fontSize: 12,
    color: '#8E9297',
    marginLeft: 4,
  },
  priorityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  priorityText: {
    fontSize: 9,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 16,
    marginBottom: 32,
  },
  quickActionCard: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
    marginTop: 8,
    textAlign: 'center',
  },
});

export default DriverDashboardScreen;
