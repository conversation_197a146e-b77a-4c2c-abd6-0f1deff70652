// Empty polyfill for Node.js modules not available in React Native
module.exports = {};

// For modules that expect specific exports
if (typeof module !== 'undefined' && module.exports) {
  module.exports.createServer = () => {};
  module.exports.request = () => {};
  module.exports.get = () => {};
  module.exports.Agent = function() {};
  module.exports.globalAgent = {};
  
  // WebSocket polyfill for 'ws' module
  module.exports.WebSocket = typeof WebSocket !== 'undefined' ? WebSocket : function() {};
  module.exports.default = typeof WebSocket !== 'undefined' ? WebSocket : function() {};
}
