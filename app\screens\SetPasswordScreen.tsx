import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import { useAlertHelpers } from '../hooks/useAlert';
import { useTranslation } from 'react-i18next';
import { VALIDATION_RULES } from '../constants/config';

interface SetPasswordScreenProps {
  onSuccess: () => void;
}

const SetPasswordScreen = ({ onSuccess }: SetPasswordScreenProps) => {
  const { t } = useTranslation();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const { showError, showSuccess } = useAlertHelpers();

  const validatePassword = (pwd: string) => {
    const minLength = pwd.length >= VALIDATION_RULES.password.minLength.value;
    const hasNumber = /\d/.test(pwd);
    const hasLetter = /[a-zA-Z]/.test(pwd);

    return {
      minLength,
      hasNumber,
      hasLetter,
      isValid: minLength && hasNumber && hasLetter
    };
  };

  const handleSetPassword = async () => {
    if (!password.trim() || !confirmPassword.trim()) {
      Alert.alert(t('common.error'), t('alerts.fillAllFields'));
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert(t('common.error'), t('auth.passwordMismatch'));
      return;
    }

    const validation = validatePassword(password);
    if (!validation.isValid) {
      Alert.alert(
        t('auth.invalidPassword'),
        t('auth.passwordRequirements')
      );
      return;
    }

    setLoading(true);
    try {
      console.log('SetPassword: Updating user password...');

      const { error } = await supabase.auth.updateUser({
        password: password
      });

      if (error) {
        console.log('SetPassword: Error updating password:', error.message);
        showError(t('common.error'), t('alerts.passwordUpdateFailed'));
        return;
      }

      console.log('SetPassword: Password updated successfully');
      showSuccess(
        t('common.success'),
        t('alerts.passwordSetSuccessfully'),
        onSuccess
      );

    } catch (error: any) {
      console.log('SetPassword: Exception:', error.message);
      showError(t('common.error'), t('alerts.unexpectedError'));
    } finally {
      setLoading(false);
    }
  };

  const validation = validatePassword(password);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>{t('auth.setPassword')}</Text>
          <Text style={styles.subtitle}>
            {t('auth.createSecurePassword')}
          </Text>
        </View>

        {/* Form */}
        <View style={styles.form}>
          {/* Password Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>{t('auth.newPassword')}</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.passwordInput}
                value={password}
                onChangeText={setPassword}
                placeholder={t('auth.enterPassword')}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setShowPassword(!showPassword)}
              >
                <Ionicons
                  name={showPassword ? 'eye-off' : 'eye'}
                  size={20}
                  color="#8E9297"
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* Confirm Password Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>{t('auth.confirmPassword')}</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.passwordInput}
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                placeholder={t('auth.enterPasswordAgain')}
                secureTextEntry={!showConfirmPassword}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                <Ionicons
                  name={showConfirmPassword ? 'eye-off' : 'eye'}
                  size={20}
                  color="#8E9297"
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* Password Requirements */}
          <View style={styles.requirementsContainer}>
            <Text style={styles.requirementsTitle}>{t('auth.passwordRequirementsTitle')}</Text>

            <View style={styles.requirement}>
              <Ionicons
                name={validation.minLength ? 'checkmark-circle' : 'ellipse-outline'}
                size={16}
                color={validation.minLength ? '#2ECC71' : '#8E9297'}
              />
              <Text style={[
                styles.requirementText,
                validation.minLength && styles.requirementMet
              ]}>
                {t('auth.minSixChars')}
              </Text>
            </View>

            <View style={styles.requirement}>
              <Ionicons
                name={validation.hasLetter ? 'checkmark-circle' : 'ellipse-outline'}
                size={16}
                color={validation.hasLetter ? '#2ECC71' : '#8E9297'}
              />
              <Text style={[
                styles.requirementText,
                validation.hasLetter && styles.requirementMet
              ]}>
                {t('auth.atLeastOneLetter')}
              </Text>
            </View>

            <View style={styles.requirement}>
              <Ionicons
                name={validation.hasNumber ? 'checkmark-circle' : 'ellipse-outline'}
                size={16}
                color={validation.hasNumber ? '#2ECC71' : '#8E9297'}
              />
              <Text style={[
                styles.requirementText,
                validation.hasNumber && styles.requirementMet
              ]}>
                {t('auth.atLeastOneNumber')}
              </Text>
            </View>
          </View>

          {/* Set Password Button */}
          <TouchableOpacity
            style={[
              styles.setButton,
              (!validation.isValid || password !== confirmPassword || loading) && styles.setButtonDisabled
            ]}
            onPress={handleSetPassword}
            disabled={!validation.isValid || password !== confirmPassword || loading}
          >
            {loading ? (
              <ActivityIndicator color="#FFFFFF" />
            ) : (
              <Text style={styles.setButtonText}>Şifreyi Belirle</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  header: {
    marginBottom: 30,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#8E9297',
    textAlign: 'center',
  },
  form: {
    flex: 1,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E1E8ED',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  eyeButton: {
    padding: 12,
  },
  requirementsContainer: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 8,
    marginBottom: 30,
  },
  requirementsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 12,
  },
  requirement: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  requirementText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#8E9297',
  },
  requirementMet: {
    color: '#2ECC71',
  },
  setButton: {
    backgroundColor: '#0D3B66',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  setButtonDisabled: {
    opacity: 0.6,
  },
  setButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SetPasswordScreen;
