
import React from 'react';

const LoginScreen = () => {
  return (
    <div className="min-h-screen bg-[#F5F8FA] flex flex-col justify-center px-6">
      <div className="max-w-sm mx-auto w-full space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-[#0D3B66] rounded-lg mx-auto"></div>
          <h1 className="text-2xl font-semibold text-[#1C1C1E]">Funeral App</h1>
          <p className="text-sm text-[#8E9297]">Cenaze takip sistemi</p>
        </div>

        {/* Login Form */}
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-[#1C1C1E]">E-posta</label>
            <div className="h-12 bg-white border border-[#E1E8ED] rounded-lg flex items-center px-4">
              <span className="text-sm text-[#8E9297]"><EMAIL></span>
            </div>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium text-[#1C1C1E]">Şifre</label>
            <div className="h-12 bg-white border border-[#E1E8ED] rounded-lg flex items-center px-4">
              <span className="text-sm text-[#8E9297]">••••••••</span>
            </div>
          </div>

          <button className="w-full h-12 bg-[#0D3B66] text-white rounded-lg font-medium">
            Giriş Yap
          </button>

          <div className="text-center">
            <span className="text-xs text-[#8E9297]">veya</span>
          </div>

          <button className="w-full h-12 bg-[#FAA916] text-white rounded-lg font-medium">
            Magic Link Gönder
          </button>
        </div>

        {/* Footer */}
        <div className="text-center">
          <a href="#" className="text-xs text-[#0D3B66]">Şifremi Unuttum</a>
        </div>
      </div>
    </div>
  );
};

export default LoginScreen;
