
import React from 'react';

const SettingsScreen = () => {
  return (
    <div className="min-h-screen bg-[#F5F8FA]">
      {/* Header */}
      <div className="bg-white border-b border-[#E1E8ED] px-4 py-4">
        <h1 className="text-lg font-semibold text-[#1C1C1E]">Ayarlar</h1>
      </div>

      <div className="p-4 space-y-6">
        {/* Profile Section */}
        <div className="bg-white rounded-lg border border-[#E1E8ED] p-4">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-16 h-16 bg-[#0D3B66] rounded-full"></div>
            <div>
              <h2 className="font-medium text-[#1C1C1E]">Ali Veli</h2>
              <p className="text-sm text-[#8E9297]"><EMAIL></p>
              <p className="text-xs text-[#8E9297]">Cenaze Direktörü</p>
            </div>
          </div>
          <button className="w-full py-2 border border-[#E1E8ED] rounded-lg text-sm text-[#0D3B66] font-medium">
            Profili Düzenle
          </button>
        </div>

        {/* Language Settings */}
        <div className="bg-white rounded-lg border border-[#E1E8ED] p-4">
          <h3 className="font-medium text-[#1C1C1E] mb-4">Dil Ayarları</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-[#1C1C1E]">Türkçe</span>
              <div className="w-5 h-5 bg-[#0D3B66] rounded-full"></div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-[#8E9297]">Deutsch</span>
              <div className="w-5 h-5 border-2 border-[#E1E8ED] rounded-full"></div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-[#8E9297]">English</span>
              <div className="w-5 h-5 border-2 border-[#E1E8ED] rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Theme Settings */}
        <div className="bg-white rounded-lg border border-[#E1E8ED] p-4">
          <h3 className="font-medium text-[#1C1C1E] mb-4">Tema</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white border-2 border-[#E1E8ED] rounded"></div>
                <span className="text-sm text-[#1C1C1E]">Açık Tema</span>
              </div>
              <div className="w-5 h-5 bg-[#0D3B66] rounded-full"></div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-[#1C1C1E] rounded"></div>
                <span className="text-sm text-[#8E9297]">Koyu Tema</span>
              </div>
              <div className="w-5 h-5 border-2 border-[#E1E8ED] rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="bg-white rounded-lg border border-[#E1E8ED] p-4">
          <h3 className="font-medium text-[#1C1C1E] mb-4">Bildirimler</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-[#1C1C1E]">Push Bildirimleri</p>
                <p className="text-xs text-[#8E9297]">Yeni görevler ve güncellemeler</p>
              </div>
              <div className="w-12 h-6 bg-[#0D3B66] rounded-full relative">
                <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-[#1C1C1E]">E-posta Bildirimleri</p>
                <p className="text-xs text-[#8E9297]">Günlük özet ve raporlar</p>
              </div>
              <div className="w-12 h-6 bg-[#E1E8ED] rounded-full relative">
                <div className="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5"></div>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-[#1C1C1E]">Acil Durum Bildirimleri</p>
                <p className="text-xs text-[#8E9297]">Yüksek öncelikli görevler</p>
              </div>
              <div className="w-12 h-6 bg-[#0D3B66] rounded-full relative">
                <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
              </div>
            </div>
          </div>
        </div>

        {/* App Settings */}
        <div className="bg-white rounded-lg border border-[#E1E8ED] p-4">
          <h3 className="font-medium text-[#1C1C1E] mb-4">Uygulama</h3>
          <div className="space-y-3">
            <button className="w-full flex items-center justify-between py-2">
              <span className="text-sm text-[#1C1C1E]">Gizlilik Politikası</span>
              <div className="w-4 h-4 bg-[#8E9297] rounded"></div>
            </button>
            <button className="w-full flex items-center justify-between py-2">
              <span className="text-sm text-[#1C1C1E]">Kullanım Şartları</span>
              <div className="w-4 h-4 bg-[#8E9297] rounded"></div>
            </button>
            <button className="w-full flex items-center justify-between py-2">
              <span className="text-sm text-[#1C1C1E]">Destek</span>
              <div className="w-4 h-4 bg-[#8E9297] rounded"></div>
            </button>
            <button className="w-full flex items-center justify-between py-2">
              <span className="text-sm text-[#1C1C1E]">Hakkında</span>
              <div className="w-4 h-4 bg-[#8E9297] rounded"></div>
            </button>
          </div>
        </div>

        {/* Logout */}
        <div className="bg-white rounded-lg border border-[#E1E8ED] p-4">
          <button className="w-full py-3 text-[#E74C3C] font-medium">
            Çıkış Yap
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsScreen;
