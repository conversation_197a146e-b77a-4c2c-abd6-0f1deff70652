// EventEmitter polyfill for React Native
// Minimal implementation of Node.js EventEmitter
// Fixed for Hermes engine compatibility

function EventEmitter() {
  if (!(this instanceof EventEmitter)) {
    return new EventEmitter();
  }
  this._events = {};
  this._maxListeners = 10;
}

// Ensure proper prototype chain for Hermes
EventEmitter.prototype = Object.create(Object.prototype);
EventEmitter.prototype.constructor = EventEmitter;

// Add methods to prototype
Object.assign(EventEmitter.prototype, {
  on: function(event, listener) {
    if (!this._events[event]) {
      this._events[event] = [];
    }
    this._events[event].push(listener);
    return this;
  },

  addListener: function(event, listener) {
    return this.on(event, listener);
  },

  once: function(event, listener) {
    const self = this;
    const onceWrapper = function(...args) {
      self.removeListener(event, onceWrapper);
      listener.apply(self, args);
    };
    return this.on(event, onceWrapper);
  },

  removeListener: function(event, listener) {
    if (!this._events[event]) return this;

    const index = this._events[event].indexOf(listener);
    if (index !== -1) {
      this._events[event].splice(index, 1);
    }

    if (this._events[event].length === 0) {
      delete this._events[event];
    }

    return this;
  },

  off: function(event, listener) {
    return this.removeListener(event, listener);
  },

  removeAllListeners: function(event) {
    if (event) {
      delete this._events[event];
    } else {
      this._events = {};
    }
    return this;
  },

  emit: function(event) {
    if (!this._events[event]) return false;

    const args = Array.prototype.slice.call(arguments, 1);
    const listeners = this._events[event].slice();
    for (let i = 0; i < listeners.length; i++) {
      try {
        listeners[i].apply(this, args);
      } catch (error) {
        console.error('EventEmitter error:', error);
      }
    }

    return true;
  },

  listeners: function(event) {
    return this._events[event] ? this._events[event].slice() : [];
  },

  listenerCount: function(event) {
    return this._events[event] ? this._events[event].length : 0;
  },

  setMaxListeners: function(n) {
    this._maxListeners = n;
    return this;
  },

  getMaxListeners: function() {
    return this._maxListeners;
  }
});

module.exports = EventEmitter;
module.exports.EventEmitter = EventEmitter;
