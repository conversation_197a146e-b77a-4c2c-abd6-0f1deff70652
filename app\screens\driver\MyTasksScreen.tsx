import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User } from '../../services/authService';
import ScreenLayout from '../../components/ScreenLayout';

interface MyTasksScreenProps {
  user: User;
  onBack: () => void;
}

const MyTasksScreen = ({ user, onBack }: MyTasksScreenProps) => {
  const [selectedFilter, setSelectedFilter] = useState<'ALL' | 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'>('ALL');

  // Mock data - gerçek uygulamada API'den gelecek
  const tasks = [
    {
      id: '1',
      title: 'Cenaze <PERSON>',
      description: 'Hastaneden camiye cenaze nakli',
      case_name: '<PERSON><PERSON> Yı<PERSON>z Vakası',
      status: 'IN_PROGRESS',
      priority: 'HIGH',
      due_time: '14:30',
      estimated_duration: '2 saat',
      location: 'Fatih Camii → Karacaahmet',
      created_at: '2024-01-20T09:00:00Z',
      progress: 60,
    },
    {
      id: '2',
      title: 'Belge Teslimi',
      description: 'Nüfus müdürlüğüne ölüm belgesi teslimi',
      case_name: 'Fatma Şahin Vakası',
      status: 'PENDING',
      priority: 'HIGH',
      due_time: '16:00',
      estimated_duration: '1 saat',
      location: 'Üsküdar Nüfus Müdürlüğü',
      created_at: '2024-01-20T08:30:00Z',
      progress: 0,
    },
    {
      id: '3',
      title: 'Aile Görüşmesi',
      description: 'Cenaze detayları için aile ile görüşme',
      case_name: 'Zeynep Arslan Vakası',
      status: 'COMPLETED',
      priority: 'MEDIUM',
      due_time: '11:00',
      estimated_duration: '1 saat',
      location: 'Ev Adresi - Beşiktaş',
      created_at: '2024-01-20T07:00:00Z',
      progress: 100,
    },
    {
      id: '4',
      title: 'Hastane Nakli',
      description: 'Morgue\'dan hastaneye nakil',
      case_name: 'Ali Kaya Vakası',
      status: 'PENDING',
      priority: 'URGENT',
      due_time: '18:00',
      estimated_duration: '1.5 saat',
      location: 'Şişli Etfal Hastanesi',
      created_at: '2024-01-20T10:15:00Z',
      progress: 0,
    },
  ];

  const statusColors = {
    PENDING: '#F39C12',
    IN_PROGRESS: '#2ECC71',
    COMPLETED: '#0D3B66',
  };

  const statusLabels = {
    PENDING: 'Bekliyor',
    IN_PROGRESS: 'Devam Ediyor',
    COMPLETED: 'Tamamlandı',
  };

  const priorityColors = {
    URGENT: '#E74C3C',
    HIGH: '#F39C12',
    MEDIUM: '#2ECC71',
    LOW: '#8E9297',
  };

  const priorityLabels = {
    URGENT: 'Acil',
    HIGH: 'Yüksek',
    MEDIUM: 'Orta',
    LOW: 'Düşük',
  };

  const filteredTasks = tasks.filter(task =>
    selectedFilter === 'ALL' || task.status === selectedFilter
  );

  const handleStartTask = (taskId: string) => {
    Alert.alert(
      'Görevi Başlat',
      'Bu görevi başlatmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Başlat', onPress: () => console.log(`Task ${taskId} started`) },
      ]
    );
  };

  const handleCompleteTask = (taskId: string) => {
    Alert.alert(
      'Görevi Tamamla',
      'Bu görevi tamamlandı olarak işaretlemek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Tamamla', onPress: () => console.log(`Task ${taskId} completed`) },
      ]
    );
  };

  const handleViewDetails = (taskId: string) => {
    Alert.alert('Görev Detayı', `Görev detayları görüntüleniyor. ID: ${taskId}`);
  };

  const handleNavigate = (taskId: string) => {
    Alert.alert('Navigasyon', 'GPS navigasyonu başlatılıyor...');
  };

  const getTaskStats = () => {
    const pending = tasks.filter(t => t.status === 'PENDING').length;
    const inProgress = tasks.filter(t => t.status === 'IN_PROGRESS').length;
    const completed = tasks.filter(t => t.status === 'COMPLETED').length;
    return { pending, inProgress, completed };
  };

  const stats = getTaskStats();

  return (
    <ScreenLayout
      title="Görevlerim"
      showBackButton
      onBack={onBack}
      contentPadding={false}
    >

      {/* Stats */}
      <View style={styles.statsSection}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.pending}</Text>
          <Text style={styles.statLabel}>Bekleyen</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.inProgress}</Text>
          <Text style={styles.statLabel}>Devam Eden</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.completed}</Text>
          <Text style={styles.statLabel}>Tamamlanan</Text>
        </View>
      </View>

      {/* Filter */}
      <View style={styles.filterSection}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {(['ALL', 'PENDING', 'IN_PROGRESS', 'COMPLETED'] as const).map((filter) => (
            <TouchableOpacity
              key={filter}
              style={[
                styles.filterButton,
                selectedFilter === filter && styles.filterButtonActive
              ]}
              onPress={() => setSelectedFilter(filter)}
            >
              <Text style={[
                styles.filterButtonText,
                selectedFilter === filter && styles.filterButtonTextActive
              ]}>
                {filter === 'ALL' ? 'Tümü' : statusLabels[filter]}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Tasks List */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {filteredTasks.map((task) => (
          <TouchableOpacity
            key={task.id}
            style={[
              styles.taskCard,
              task.priority === 'URGENT' && styles.urgentTaskCard
            ]}
            onPress={() => handleViewDetails(task.id)}
          >
            <View style={styles.taskHeader}>
              <View style={styles.taskInfo}>
                <View style={styles.taskTitleRow}>
                  <Text style={styles.taskTitle}>{task.title}</Text>
                  {task.priority === 'URGENT' && (
                    <Ionicons name="warning" size={16} color="#E74C3C" />
                  )}
                </View>
                <Text style={styles.taskDescription}>{task.description}</Text>
                <Text style={styles.caseName}>{task.case_name}</Text>

                <View style={styles.taskMeta}>
                  <View style={[
                    styles.statusBadge,
                    { backgroundColor: statusColors[task.status as keyof typeof statusColors] }
                  ]}>
                    <Text style={styles.statusText}>
                      {statusLabels[task.status as keyof typeof statusLabels]}
                    </Text>
                  </View>
                  <View style={[
                    styles.priorityBadge,
                    { backgroundColor: priorityColors[task.priority as keyof typeof priorityColors] }
                  ]}>
                    <Text style={styles.priorityText}>
                      {priorityLabels[task.priority as keyof typeof priorityLabels]}
                    </Text>
                  </View>
                </View>
              </View>
            </View>

            {/* Progress Bar */}
            {task.status === 'IN_PROGRESS' && (
              <View style={styles.progressSection}>
                <View style={styles.progressHeader}>
                  <Text style={styles.progressLabel}>İlerleme</Text>
                  <Text style={styles.progressPercent}>{task.progress}%</Text>
                </View>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      { width: `${task.progress}%` }
                    ]}
                  />
                </View>
              </View>
            )}

            {/* Task Details */}
            <View style={styles.taskDetails}>
              <View style={styles.detailRow}>
                <View style={styles.detailItem}>
                  <Ionicons name="time" size={14} color="#8E9297" />
                  <Text style={styles.detailText}>{task.due_time}</Text>
                </View>
                <View style={styles.detailItem}>
                  <Ionicons name="hourglass" size={14} color="#8E9297" />
                  <Text style={styles.detailText}>{task.estimated_duration}</Text>
                </View>
              </View>

              <View style={styles.detailRow}>
                <View style={styles.detailItem}>
                  <Ionicons name="location" size={14} color="#8E9297" />
                  <Text style={styles.detailText}>{task.location}</Text>
                </View>
              </View>

              {/* Action Buttons */}
              <View style={styles.actionButtons}>
                <TouchableOpacity
                  style={styles.navigateButton}
                  onPress={() => handleNavigate(task.id)}
                >
                  <Ionicons name="navigate" size={16} color="#2ECC71" />
                  <Text style={styles.navigateButtonText}>Yol Tarifi</Text>
                </TouchableOpacity>

                {task.status === 'PENDING' && (
                  <TouchableOpacity
                    style={styles.startButton}
                    onPress={() => handleStartTask(task.id)}
                  >
                    <Ionicons name="play" size={16} color="#FFFFFF" />
                    <Text style={styles.startButtonText}>Başlat</Text>
                  </TouchableOpacity>
                )}

                {task.status === 'IN_PROGRESS' && (
                  <TouchableOpacity
                    style={styles.completeButton}
                    onPress={() => handleCompleteTask(task.id)}
                  >
                    <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                    <Text style={styles.completeButtonText}>Tamamla</Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </TouchableOpacity>
        ))}

        {filteredTasks.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="checkmark-circle-outline" size={64} color="#E1E8ED" />
            <Text style={styles.emptyStateTitle}>Görev bulunamadı</Text>
            <Text style={styles.emptyStateText}>
              {selectedFilter === 'ALL'
                ? 'Henüz size atanmış görev bulunmuyor.'
                : `${statusLabels[selectedFilter]} durumunda görev bulunmuyor.`
              }
            </Text>
          </View>
        )}
      </ScrollView>
    </ScreenLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  placeholder: {
    width: 40,
  },
  statsSection: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingVertical: 8,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2ECC71',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 10,
    color: '#8E9297',
  },
  filterSection: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: '#2ECC71',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8E9297',
  },
  filterButtonTextActive: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  taskCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  urgentTaskCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#E74C3C',
  },
  taskHeader: {
    marginBottom: 16,
  },
  taskInfo: {
    flex: 1,
  },
  taskTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    flex: 1,
    marginRight: 8,
  },
  taskDescription: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 4,
  },
  caseName: {
    fontSize: 12,
    color: '#2ECC71',
    fontWeight: '500',
    marginBottom: 8,
  },
  taskMeta: {
    flexDirection: 'row',
    gap: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  progressSection: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
  },
  progressPercent: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2ECC71',
  },
  progressBar: {
    height: 6,
    backgroundColor: '#E1E8ED',
    borderRadius: 3,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#2ECC71',
    borderRadius: 3,
  },
  taskDetails: {
    borderTopWidth: 1,
    borderTopColor: '#F5F8FA',
    paddingTop: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  detailText: {
    fontSize: 12,
    color: '#8E9297',
    marginLeft: 6,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 12,
  },
  navigateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F8FA',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
    borderWidth: 1,
    borderColor: '#2ECC71',
  },
  navigateButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2ECC71',
    marginLeft: 4,
  },
  startButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F39C12',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
  },
  startButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 4,
  },
  completeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#2ECC71',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
  },
  completeButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 4,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#8E9297',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#B8C5D1',
    textAlign: 'center',
    paddingHorizontal: 32,
  },
});

export default MyTasksScreen;
