import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ActivityIndicator,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import { useAlertHelpers } from '../hooks/useAlert';
import { useTranslation } from '../i18n/useTranslation';
import { emailService } from '../services/emailService';
import { authService } from '../services/authService';

interface ForgotPasswordScreenProps {
  onBack: () => void;
}

const ForgotPasswordScreen = ({ onBack }: ForgotPasswordScreenProps) => {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'email' | 'sent'>('email');

  const { showError, showInfo } = useAlertHelpers();

  const handleResetPassword = async () => {
    if (!email.trim()) {
      showError(t('common.error'), t('alerts.enterEmail'));
      return;
    }

    setLoading(true);
    try {
      console.log('ForgotPassword: Checking if user exists...');

      // Önce kullanıcının sistemde olup olmadığını kontrol et
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('email, full_name')
        .eq('email', email.trim().toLowerCase())
        .single();

      if (userError || !user) {
        console.log('ForgotPassword: User not found:', userError?.message);
        showError(
          t('alerts.userNotFound'),
          t('auth.emailNotRegistered')
        );
        return;
      }

      console.log('ForgotPassword: User found, checking auth status...');

      // Kullanıcının auth.users'da olup olmadığını kontrol et
      const userStatus = await authService.checkUserStatus(email.trim().toLowerCase());
      
      if (userStatus.status === 'NEEDS_PASSWORD') {
        showError(
          t('alerts.accountNotFoundCreateFirst'),
          'Henüz şifre belirlememiş görünüyorsunuz. Lütfen "Hesap Oluştur" butonunu kullanın.'
        );
        return;
      }
      
      if (userStatus.status === 'NOT_FOUND') {
        showError(
          t('alerts.userNotFound'),
          t('auth.emailNotRegistered')
        );
        return;
      }

      // Kullanıcı auth.users'da var, şifre sıfırlama e-postası gönder
      const { error } = await supabase.auth.resetPasswordForEmail(
        email.trim().toLowerCase(),
        {
          redirectTo: undefined, // Mobile app için gerekli değil
        }
      );

      if (error) {
        console.log('ForgotPassword: Reset email error:', error.message);

        // Kullanıcı dostu hata mesajları
        let errorMessage = t('alerts.passwordResetEmailFailed');

        if (error.message.includes('Email not confirmed')) {
          errorMessage = t('auth.emailNotConfirmed');
        } else if (error.message.includes('rate limit')) {
          errorMessage = t('auth.tooManyAttempts');
        }

        showError(t('common.error'), errorMessage);
        return;
      }

      console.log('ForgotPassword: Reset email sent successfully');
      setStep('sent');

    } catch (error: any) {
      console.log('ForgotPassword: Exception:', error.message);
      showError(t('common.error'), t('alerts.unexpectedError'));
    } finally {
      setLoading(false);
    }
  };

  const handleResendEmail = () => {
    setStep('email');
    handleResetPassword();
  };

  if (step === 'sent') {
    return (
      <View style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.content}>
            {/* Header */}
            <View style={styles.header}>
              <TouchableOpacity onPress={onBack} style={styles.backButton}>
                <Ionicons name="arrow-back" size={24} color="#0D3B66" />
              </TouchableOpacity>
              <Text style={styles.title}>{t('alerts.checkYourEmail')}</Text>
            </View>

            {/* Email Sent Content */}
            <View style={styles.emailSentContent}>
              <View style={styles.emailIcon}>
                <Ionicons name="mail" size={64} color="#0D3B66" />
              </View>

              <Text style={styles.emailSentTitle}>{t('alerts.passwordResetEmailSent')}</Text>
              <Text style={styles.emailSentText}>
                {t('alerts.passwordResetEmailSentMessage', { email })}
                E-postanızı kontrol edin ve talimatları takip edin.
              </Text>

              <TouchableOpacity style={styles.resendButton} onPress={handleResendEmail}>
                <Text style={styles.resendText}>Tekrar Gönder</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.changeEmailButton} onPress={() => setStep('email')}>
                <Text style={styles.changeEmailText}>E-posta Adresini Değiştir</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.backToLoginButton} onPress={onBack}>
                <Text style={styles.backToLoginText}>Giriş Sayfasına Dön</Text>
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={onBack} style={styles.backButton}>
              <Ionicons name="arrow-back" size={24} color="#0D3B66" />
            </TouchableOpacity>
            <Text style={styles.title}>{t('auth.forgotPassword')}</Text>
          </View>

          {/* Form */}
          <View style={styles.form}>
            <Text style={styles.description}>
              {t('alerts.enterEmailForPasswordReset')}
            </Text>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>E-posta Adresi</Text>
              <TextInput
                style={styles.input}
                value={email}
                onChangeText={setEmail}
                placeholder={emailService.getPlaceholderEmail()}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <TouchableOpacity
              style={[styles.resetButton, loading && styles.resetButtonDisabled]}
              onPress={handleResetPassword}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#FFFFFF" />
              ) : (
                <Text style={styles.resetButtonText}>Sıfırlama Linki Gönder</Text>
              )}
            </TouchableOpacity>

            <View style={styles.infoBox}>
              <Ionicons name="information-circle" size={20} color="#0D3B66" />
              <Text style={styles.infoText}>
                E-postanızda link bulamazsanız spam klasörünü kontrol edin.
              </Text>
            </View>
          </View>
        </View>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  safeArea: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 30,
  },
  backButton: {
    marginRight: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  form: {
    flex: 1,
  },
  description: {
    fontSize: 16,
    color: '#8E9297',
    lineHeight: 24,
    marginBottom: 30,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#E1E8ED',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
  },
  resetButton: {
    backgroundColor: '#0D3B66',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  resetButtonDisabled: {
    opacity: 0.6,
  },
  resetButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: '#E3F2FD',
    padding: 16,
    borderRadius: 8,
    alignItems: 'flex-start',
  },
  infoText: {
    flex: 1,
    marginLeft: 10,
    fontSize: 14,
    color: '#0D3B66',
    lineHeight: 20,
  },
  emailSentContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emailIcon: {
    marginBottom: 30,
  },
  emailSentTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 16,
    textAlign: 'center',
  },
  emailSentText: {
    fontSize: 16,
    color: '#8E9297',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  resendButton: {
    backgroundColor: '#0D3B66',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  resendText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  changeEmailButton: {
    paddingVertical: 12,
    marginBottom: 16,
  },
  changeEmailText: {
    color: '#0D3B66',
    fontSize: 16,
  },
  backToLoginButton: {
    paddingVertical: 12,
  },
  backToLoginText: {
    color: '#8E9297',
    fontSize: 16,
  },
});

export default ForgotPasswordScreen;
