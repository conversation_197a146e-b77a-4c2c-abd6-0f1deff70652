import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User } from '../../services/authService';
import { caseService, CaseData } from '../../services/caseService';
import { simpleCaseService } from '../../services/simpleCaseService';
import { useTranslation } from '../../i18n/useTranslation';

interface CasesManagementScreenProps {
  user: User;
  onBack: () => void;
}

const CasesManagementScreen = ({ user, onBack }: CasesManagementScreenProps) => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<'ALL' | 'OPEN' | 'CLOSED' | 'CANCELLED'>('ALL');
  const [loading, setLoading] = useState(true);
  const [cases, setCases] = useState<CaseData[]>([]);

  useEffect(() => {
    loadCases();
  }, []);

  const loadCases = async () => {
    try {
      setLoading(true);
      console.log('CasesManagementScreen: Starting to load cases...');

      // Test simple query first
      const simpleData = await simpleCaseService.getAllCases();
      console.log('CasesManagementScreen: Simple cases loaded:', simpleData.length);

      // Try with relations
      const data = await simpleCaseService.getCasesWithAll();
      console.log('CasesManagementScreen: Cases with relations loaded:', data.length);

      setCases(data);
    } catch (error) {
      console.error('Error loading cases:', error);
      Alert.alert(t('common.error'), t('alerts.loadingError') + ': ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const statusColors = {
    OPEN: '#F39C12',
    CLOSED: '#2ECC71',
    CANCELLED: '#E74C3C',
  };

  const statusLabels = {
    OPEN: 'Açık',
    CLOSED: 'Kapalı',
    CANCELLED: 'İptal Edildi',
  };

  const burialTypeLabels = {
    DE: '🇩🇪 Almanya',
    TR: '🇹🇷 Türkiye',
  };

  const filteredCases = cases.filter(c => {
    const matchesSearch = (c.deceased?.full_name || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (c.deceased?.family_name || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (c.family_user?.full_name || '').toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = selectedStatus === 'ALL' || c.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  const handleCreateCase = () => {
    Alert.alert(t('alerts.newCase'), t('alerts.newCaseFeature'));
  };

  const handleViewCase = (caseId: string) => {
    Alert.alert(t('alerts.caseDetails'), t('alerts.caseDetailFeature', { id: caseId }));
  };

  const handleAssignDriver = (caseId: string) => {
    Alert.alert(t('alerts.assignDriver'), t('alerts.assignDriverFeature', { id: caseId }));
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#0D3B66" />
        </TouchableOpacity>
        <Text style={styles.title}>Vaka Yönetimi</Text>
        <TouchableOpacity onPress={handleCreateCase} style={styles.addButton}>
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Search and Filter */}
      <View style={styles.searchSection}>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={16} color="#8E9297" />
          <TextInput
            style={styles.searchInput}
            placeholder="Vaka ara..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#8E9297"
          />
        </View>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterContainer}>
          {(['ALL', 'OPEN', 'CLOSED', 'CANCELLED'] as const).map((status) => (
            <TouchableOpacity
              key={status}
              style={[
                styles.filterButton,
                selectedStatus === status && styles.filterButtonActive
              ]}
              onPress={() => setSelectedStatus(status)}
            >
              <Text style={[
                styles.filterButtonText,
                selectedStatus === status && styles.filterButtonTextActive
              ]}>
                {status === 'ALL' ? 'Tümü' : statusLabels[status]}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Cases List */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0D3B66" />
            <Text style={styles.loadingText}>{t('cases.loadingCases')}</Text>
          </View>
        ) : (
          <>
            <View style={styles.statsRow}>
              <Text style={styles.statsText}>
                {filteredCases.length} vaka bulundu
              </Text>
            </View>

        {filteredCases.map((caseData) => (
          <TouchableOpacity
            key={caseData.id}
            style={styles.caseCard}
            onPress={() => handleViewCase(caseData.id)}
          >
            <View style={styles.caseHeader}>
              <View style={styles.caseInfo}>
                <Text style={styles.deceasedName}>{caseData.deceased?.full_name || 'Bilinmeyen'}</Text>
                <Text style={styles.familyContact}>
                  Aile: {caseData.deceased?.family_name || caseData.family_user?.full_name || 'Bilinmeyen'}
                </Text>
                <View style={styles.caseMeta}>
                  <View style={[
                    styles.statusBadge,
                    { backgroundColor: statusColors[caseData.status as keyof typeof statusColors] }
                  ]}>
                    <Text style={styles.statusText}>
                      {statusLabels[caseData.status as keyof typeof statusLabels]}
                    </Text>
                  </View>
                  <View style={[
                    styles.priorityBadge,
                    { backgroundColor: caseData.burial_type === 'DE' ? '#1976D2' : '#D32F2F' }
                  ]}>
                    <Text style={styles.priorityText}>
                      {burialTypeLabels[caseData.burial_type as keyof typeof burialTypeLabels]}
                    </Text>
                  </View>
                </View>
              </View>
              <View style={styles.caseActions}>
                <Text style={styles.caseDate}>
                  {new Date(caseData.created_at).toLocaleDateString('tr-TR')}
                </Text>
                <Ionicons name="chevron-forward" size={20} color="#8E9297" />
              </View>
            </View>



            {/* Case Details */}
            <View style={styles.caseDetails}>
              <View style={styles.detailRow}>
                <View style={styles.detailItem}>
                  <Ionicons name="calendar" size={16} color="#8E9297" />
                  <Text style={styles.detailText}>
                    {caseData.deceased?.date_of_death ?
                      new Date(caseData.deceased.date_of_death).toLocaleDateString('tr-TR') :
                      'Tarih belirtilmemiş'
                    }
                  </Text>
                </View>
                <View style={styles.detailItem}>
                  <Ionicons name="location" size={16} color="#8E9297" />
                  <Text style={styles.detailText}>
                    {caseData.deceased?.place_of_burial || 'Defin yeri belirtilmemiş'}
                  </Text>
                </View>
              </View>

              {/* Tasks Summary */}
              {caseData.tasks && caseData.tasks.length > 0 && (
                <View style={styles.tasksSection}>
                  <Text style={styles.tasksTitle}>Görevler ({caseData.tasks.length})</Text>
                  {caseData.tasks.slice(0, 2).map((task) => (
                    <View key={task.id} style={styles.taskItem}>
                      <Text style={styles.taskType}>
                        {task.task_type === 'PICK_UP_FROM_MORGUE' ? 'Morgdan Alma' :
                         task.task_type === 'TO_AIRPORT' ? 'Havaalanına Götürme' :
                         task.task_type === 'TO_CONSULATE' ? 'Konsolosluğa Götürme' :
                         task.task_type === 'DELIVERED' ? 'Teslim Edildi' : task.task_type}
                      </Text>
                      <Text style={[styles.taskStatus, {
                        color: task.status === 'DONE' ? '#2ECC71' :
                               task.status === 'ACTIVE' ? '#F39C12' : '#8E9297'
                      }]}>
                        {task.status === 'DONE' ? 'Tamamlandı' :
                         task.status === 'ACTIVE' ? 'Aktif' :
                         task.status === 'PENDING' ? 'Bekliyor' : task.status}
                      </Text>
                    </View>
                  ))}
                </View>
              )}
            </View>
          </TouchableOpacity>
        ))}

        {filteredCases.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="folder-outline" size={64} color="#E1E8ED" />
            <Text style={styles.emptyStateTitle}>Vaka bulunamadı</Text>
            <Text style={styles.emptyStateText}>
              Arama kriterlerinize uygun vaka bulunmuyor.
            </Text>
          </View>
        )}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#0D3B66',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchSection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingTop: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F8FA',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 12,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#1C1C1E',
  },
  filterContainer: {
    flexDirection: 'row',
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F5F8FA',
    marginRight: 6,
  },
  filterButtonActive: {
    backgroundColor: '#0D3B66',
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#8E9297',
  },
  filterButtonTextActive: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  statsRow: {
    paddingVertical: 12,
  },
  statsText: {
    fontSize: 14,
    color: '#8E9297',
    fontWeight: '500',
  },
  caseCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E1E8ED',
    shadowColor: '#000',
    shadowOffset: {
      width: -2,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  caseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  caseInfo: {
    flex: 1,
  },
  deceasedName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  familyContact: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 8,
  },
  caseMeta: {
    flexDirection: 'row',
    gap: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  caseActions: {
    alignItems: 'flex-end',
  },
  caseDate: {
    fontSize: 12,
    color: '#8E9297',
    marginBottom: 8,
  },
  progressSection: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
  },
  progressPercent: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0D3B66',
  },
  progressBar: {
    height: 6,
    backgroundColor: '#E1E8ED',
    borderRadius: 3,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  caseDetails: {
    borderTopWidth: 1,
    borderTopColor: '#F5F8FA',
    paddingTop: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  detailText: {
    fontSize: 12,
    color: '#8E9297',
    marginLeft: 6,
  },
  assignButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#2ECC71',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  assignButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 4,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#8E9297',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#B8C5D1',
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#8E9297',
    textAlign: 'center',
  },
  tasksSection: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F5F8FA',
  },
  tasksTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  taskItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  taskType: {
    fontSize: 11,
    color: '#8E9297',
    flex: 1,
  },
  taskStatus: {
    fontSize: 10,
    fontWeight: '500',
  },
});

export default CasesManagementScreen;
