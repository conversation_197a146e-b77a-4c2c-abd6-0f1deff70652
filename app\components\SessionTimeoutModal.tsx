import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { APP_CONFIG } from '../constants/config';
import { useNotificationSound } from '../hooks/useNotificationSound';

interface SessionTimeoutModalProps {
  visible: boolean;
  onExtendSession: () => void;
  onLogout: () => void;
}

const SessionTimeoutModal: React.FC<SessionTimeoutModalProps> = ({
  visible,
  onExtendSession,
  onLogout,
}) => {
  // 3 dakika countdown (5 dakika timeout - 2 dakika uyarı = 3 dakika kalan)
  const COUNTDOWN_MINUTES = 3;
  const [countdown, setCountdown] = useState(COUNTDOWN_MINUTES * 60);
  const { playNotificationSound } = useNotificationSound();

  // onLogout'u useCallback ile wrap ederek render sırasında çağrılmasını önle
  const handleLogout = useCallback(() => {
    console.log('SessionTimeoutModal: ⏰ Logout işlemi başlatılıyor (async)');
    // setTimeout ile async hale getir - render sırasında state güncellemesini önle
    setTimeout(() => {
      onLogout();
    }, 0);
  }, [onLogout]);

  useEffect(() => {
    if (!visible) {
      setCountdown(COUNTDOWN_MINUTES * 60);
      console.log('SessionTimeoutModal: 📱 Modal kapatıldı, countdown sıfırlandı');
      return;
    }

    console.log(`SessionTimeoutModal: 📱 Modal açıldı, ${COUNTDOWN_MINUTES} dakika (${COUNTDOWN_MINUTES * 60} saniye) countdown başlatılıyor`);
    
    // Play notification sound when timeout warning appears
    playNotificationSound();
    
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          console.log('SessionTimeoutModal: ⏰ Countdown sıfıra ulaştı, otomatik logout başlatılıyor');
          handleLogout(); // Async logout çağrısı
          return 0;
        }
        
        // Her 30 saniyede bir log
        if (prev % 30 === 0) {
          console.log(`SessionTimeoutModal: ⏱️ Countdown: ${prev} saniye kaldı`);
        }
        
        return prev - 1;
      });
    }, 1000);

    return () => {
      console.log('SessionTimeoutModal: 🧹 Timer temizleniyor');
      clearInterval(timer);
    };
  }, [visible, handleLogout]);

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Icon */}
          <View style={styles.iconContainer}>
            <Ionicons name="time-outline" size={48} color="#F39C12" />
          </View>

          {/* Title */}
          <Text style={styles.title}>Oturum Süresi Dolmak Üzere</Text>

          {/* Message */}
          <Text style={styles.message}>
            Güvenlik nedeniyle oturumunuz yakında sonlandırılacak.
          </Text>

          {/* Countdown */}
          <View style={styles.countdownContainer}>
            <Text style={styles.countdownLabel}>Kalan süre:</Text>
            <Text style={styles.countdownTime}>{formatTime(countdown)}</Text>
          </View>

          {/* Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.logoutButton]}
              onPress={handleLogout}
              activeOpacity={0.8}
            >
              <Text style={styles.logoutButtonText}>Çıkış Yap</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.extendButton]}
              onPress={onExtendSession}
              activeOpacity={0.8}
            >
              <Text style={styles.extendButtonText}>Oturumu Uzat</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    width: width - 40,
    maxWidth: 400,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#FEF9E7',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    textAlign: 'center',
    marginBottom: 8,
  },
  message: {
    fontSize: 16,
    color: '#8E9297',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  countdownContainer: {
    backgroundColor: '#F5F8FA',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 24,
    width: '100%',
  },
  countdownLabel: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 4,
  },
  countdownTime: {
    fontSize: 32,
    fontWeight: '700',
    color: '#F39C12',
    fontFamily: 'monospace',
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoutButton: {
    backgroundColor: '#F5F8FA',
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  logoutButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#8E9297',
  },
  extendButton: {
    backgroundColor: '#007AFF',
  },
  extendButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

export default SessionTimeoutModal;
