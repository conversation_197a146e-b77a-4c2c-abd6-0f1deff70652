import 'react-native-url-polyfill/auto';
import React, { useRef, useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer, NavigationContainerRef } from '@react-navigation/native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import RootNavigator from './app/navigation/RootNavigator';
import { navigationActivityTracker } from './app/utils/navigationActivityTracker';
import { AlertProvider } from './app/hooks/useAlert';
import './app/i18n'; // Initialize i18n
import i18n from './app/i18n';

// Suppress Supabase GoTrue warning about stack guards
// Fixed for Hermes engine compatibility
const originalWarn = console.warn;
console.warn = function() {
  const args = Array.prototype.slice.call(arguments);
  const message = args[0];
  if (typeof message === 'string' && message.indexOf('@supabase/gotrue-js: Stack guards not supported') !== -1) {
    return; // Suppress this specific warning
  }
  originalWarn.apply(console, args);
};

const queryClient = new QueryClient();

export default function App() {
  const navigationRef = useRef<NavigationContainerRef<any>>(null);
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language);

  useEffect(() => {
    const handleLanguageChange = (lng: string) => {
      setCurrentLanguage(lng);
    };

    i18n.on('languageChanged', handleLanguageChange);

    return () => {
      i18n.off('languageChanged', handleLanguageChange);
    };
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <QueryClientProvider client={queryClient}>
          <AlertProvider>
            <NavigationContainer
              key={currentLanguage}
              ref={navigationRef}
              onReady={() => {
                if (navigationRef.current) {
                  navigationActivityTracker.setNavigationRef(navigationRef.current);
                }
              }}
            >
              <RootNavigator />
              <StatusBar style="auto" />
            </NavigationContainer>
          </AlertProvider>
        </QueryClientProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
