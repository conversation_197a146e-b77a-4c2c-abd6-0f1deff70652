import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { adminService, CreateUserData } from '../services/adminService';

// Almanya'daki başlıca şehirler listesi
const GERMAN_CITIES = [
  'Berlin', 'Hamburg', 'München', 'Köln', 'Frankfurt am Main',
  'Stuttgart', 'Düsseldorf', 'Dortmund', 'Essen', 'Leipzig',
  'Bremen', 'Dresden', 'Hannover', 'Nürnberg', 'Duisburg',
  'Bochum', 'Wupper<PERSON>', 'Bielefeld', 'Bonn', 'Münster',
  'Karlsruhe', 'Mannheim', 'Augsburg', 'Wiesbaden', 'Gelsenkirchen',
  'Mönchengladbach', 'Braunschwei<PERSON>', '<PERSON><PERSON><PERSON>', 'Kiel', '<PERSON><PERSON><PERSON>',
  'Halle (Saale)', 'Magdeburg', 'Freiburg im Breisgau', 'Krefeld',
  '<PERSON><PERSON><PERSON>', 'Oberhausen', 'Erfurt', 'Mainz', 'Rostock', 'Kassel',
  'Hagen', 'Hamm', 'Saarbrücken', 'Mülheim an der Ruhr', 'Potsdam',
  'Ludwigshafen am Rhein', 'Oldenburg', 'Leverkusen', 'Osnabrück',
  'Solingen', 'Heidelberg', 'Herne', 'Neuss', 'Darmstadt',
  'Paderborn', 'Regensburg', 'Ingolstadt', 'Würzburg', 'Fürth',
  'Wolfsburg', 'Offenbach am Main', 'Ulm', 'Heilbronn', 'Pforzheim',
  'Göttingen', 'Bottrop', 'Trier', 'Recklinghausen', 'Reutlingen',
  'Bremerhaven', 'Koblenz', 'Bergisch Gladbach', 'Jena', 'Remscheid',
  'Erlangen', 'Moers', 'Siegen', 'Hildesheim', 'Salzgitter'
].sort();

interface AddDriverModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const AddDriverModal: React.FC<AddDriverModalProps> = ({
  visible,
  onClose,
  onSuccess,
}) => {
  const [formData, setFormData] = useState<CreateUserData>({
    email: '',
    full_name: '',
    role: 'DRIVER',
    phone: '',
    location: '',
    status: 'ACTIVE',
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<CreateUserData>>({});
  const [showCityPicker, setShowCityPicker] = useState(false);
  const [citySearchQuery, setCitySearchQuery] = useState('');

  const validateForm = (): boolean => {
    const newErrors: Partial<CreateUserData> = {};

    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Ad Soyad gerekli';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'E-posta gerekli';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Geçerli bir e-posta adresi girin';
    }

    if (!formData.phone?.trim()) {
      newErrors.phone = 'Telefon numarası gerekli';
    }

    if (!formData.location?.trim()) {
      newErrors.location = 'Şehir gerekli';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const result = await adminService.createUser(formData);

      if (result.success) {
        Alert.alert(
          '✅ Başarılı',
          'Sürücü başarıyla eklendi. Kullanıcı şifre oluşturmak için "Hesap Oluştur" sayfasını kullanabilir.',
          [
            {
              text: 'Tamam',
              style: 'default',
              onPress: () => {
                resetForm();
                onSuccess();
                onClose();
              },
            },
          ],
          { cancelable: false }
        );
      } else {
        Alert.alert(
          '⚠️ Hata',
          result.error || 'Sürücü eklenirken beklenmeyen bir hata oluştu.',
          [
            {
              text: 'Tamam',
              style: 'default',
            },
          ],
          { cancelable: true }
        );
      }
    } catch (error) {
      console.error('Error creating driver:', error);
      Alert.alert(
        '❌ Sistem Hatası',
        'Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin veya yöneticinizle iletişime geçin.',
        [
          {
            text: 'Tamam',
            style: 'default',
          },
        ],
        { cancelable: true }
      );
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      email: '',
      full_name: '',
      role: 'DRIVER',
      phone: '',
      location: '',
      status: 'ACTIVE',
    });
    setErrors({});
    setCitySearchQuery('');
  };

  const handleClose = () => {
    resetForm();
    setShowCityPicker(false);
    onClose();
  };

  const filteredCities = GERMAN_CITIES.filter(city =>
    city.toLowerCase().includes(citySearchQuery.toLowerCase())
  );

  const handleCitySelect = (city: string) => {
    setFormData({ ...formData, location: city });
    setShowCityPicker(false);
    setCitySearchQuery('');
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          style={styles.keyboardAvoid}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#8E9297" />
            </TouchableOpacity>
            <Text style={styles.title}>Yeni Sürücü Ekle</Text>
            <View style={styles.headerRight} />
          </View>

          <ScrollView
            style={styles.content}
            contentContainerStyle={styles.contentContainer}
            showsVerticalScrollIndicator={false}
          >
            {/* Form Fields */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Ad Soyad *</Text>
              <TextInput
                style={[styles.input, errors.full_name && styles.inputError]}
                value={formData.full_name}
                onChangeText={(text) => setFormData({ ...formData, full_name: text })}
                placeholder="Örn: Mehmet Yılmaz"
                placeholderTextColor="#8E9297"
              />
              {errors.full_name && <Text style={styles.errorText}>{errors.full_name}</Text>}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>E-posta *</Text>
              <TextInput
                style={[styles.input, errors.email && styles.inputError]}
                value={formData.email}
                onChangeText={(text) => setFormData({ ...formData, email: text.toLowerCase() })}
                placeholder="<EMAIL>"
                placeholderTextColor="#8E9297"
                keyboardType="email-address"
                autoCapitalize="none"
              />
              {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Telefon *</Text>
              <TextInput
                style={[styles.input, errors.phone && styles.inputError]}
                value={formData.phone}
                onChangeText={(text) => setFormData({ ...formData, phone: text })}
                placeholder="+49123456789"
                placeholderTextColor="#8E9297"
                keyboardType="phone-pad"
              />
              {errors.phone && <Text style={styles.errorText}>{errors.phone}</Text>}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Şehir *</Text>
              <TouchableOpacity
                style={[styles.citySelector, errors.location && styles.inputError]}
                onPress={() => setShowCityPicker(true)}
              >
                <Text style={[
                  styles.citySelectorText,
                  !formData.location && styles.citySelectorPlaceholder
                ]}>
                  {formData.location || 'Şehir seçin'}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#8E9297" />
              </TouchableOpacity>
              {errors.location && <Text style={styles.errorText}>{errors.location}</Text>}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Durum</Text>
              <View style={styles.statusContainer}>
                {(['ACTIVE', 'ON_LEAVE', 'INACTIVE'] as const).map((status) => (
                  <TouchableOpacity
                    key={status}
                    style={[
                      styles.statusButton,
                      formData.status === status && styles.statusButtonActive,
                      status === 'ACTIVE' && styles.statusButtonGreen,
                      status === 'ON_LEAVE' && styles.statusButtonOrange,
                      status === 'INACTIVE' && styles.statusButtonRed,
                    ]}
                    onPress={() => setFormData({ ...formData, status })}
                  >
                    <Text style={[
                      styles.statusButtonText,
                      formData.status === status && styles.statusButtonTextActive
                    ]}>
                      {status === 'ACTIVE' ? 'Aktif' :
                       status === 'ON_LEAVE' ? 'İzinli' : 'Pasif'}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.infoBox}>
              <Ionicons name="information-circle" size={20} color="#0D3B66" />
              <Text style={styles.infoText}>
                Sürücü eklendikten sonra, e-posta adresini kullanarak "Hesap Oluştur" sayfasından şifre oluşturabilir.
              </Text>
            </View>

            {/* Extra bottom padding for scroll */}
            <View style={styles.bottomPadding} />
          </ScrollView>

          {/* Footer */}
          <View style={styles.footer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleClose}
              disabled={loading}
            >
              <Text style={styles.cancelButtonText}>İptal</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.submitButton, loading && styles.submitButtonDisabled]}
              onPress={handleSubmit}
              disabled={loading}
            >
              <Text style={styles.submitButtonText}>
                {loading ? 'Ekleniyor...' : 'Sürücü Ekle'}
              </Text>
            </TouchableOpacity>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>

      {/* City Picker Modal */}
      <Modal
        visible={showCityPicker}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCityPicker(false)}
      >
        <SafeAreaView style={styles.cityPickerContainer}>
          {/* City Picker Header */}
          <View style={styles.cityPickerHeader}>
            <TouchableOpacity
              onPress={() => setShowCityPicker(false)}
              style={styles.cityPickerCloseButton}
            >
              <Ionicons name="close" size={24} color="#8E9297" />
            </TouchableOpacity>
            <Text style={styles.cityPickerTitle}>Şehir Seçin</Text>
            <View style={styles.cityPickerHeaderRight} />
          </View>

          {/* Search Input */}
          <View style={styles.citySearchContainer}>
            <Ionicons name="search" size={16} color="#8E9297" />
            <TextInput
              style={styles.citySearchInput}
              placeholder="Şehir ara..."
              value={citySearchQuery}
              onChangeText={setCitySearchQuery}
              placeholderTextColor="#8E9297"
            />
          </View>

          {/* Cities List */}
          <FlatList
            data={filteredCities}
            keyExtractor={(item) => item}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.cityItem}
                onPress={() => handleCitySelect(item)}
              >
                <Text style={styles.cityItemText}>{item}</Text>
                {formData.location === item && (
                  <Ionicons name="checkmark" size={20} color="#0D3B66" />
                )}
              </TouchableOpacity>
            )}
            showsVerticalScrollIndicator={false}
          />
        </SafeAreaView>
      </Modal>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  keyboardAvoid: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  closeButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#0D3B66',
  },
  headerRight: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0D3B66',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#0D3B66',
  },
  inputError: {
    borderColor: '#EF4444',
  },
  errorText: {
    fontSize: 14,
    color: '#EF4444',
    marginTop: 4,
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: '#EBF4FF',
    padding: 16,
    borderRadius: 12,
    marginTop: 10,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#0D3B66',
    marginLeft: 8,
    lineHeight: 20,
  },
  bottomPadding: {
    height: 40,
  },
  footer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
  },
  submitButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 12,
    backgroundColor: '#0D3B66',
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  statusContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  statusButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
  },
  statusButtonActive: {
    borderWidth: 2,
  },
  statusButtonGreen: {
    borderColor: '#2ECC71',
  },
  statusButtonOrange: {
    borderColor: '#F39C12',
  },
  statusButtonRed: {
    borderColor: '#E74C3C',
  },
  statusButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  statusButtonTextActive: {
    color: '#0D3B66',
    fontWeight: '600',
  },
  // City Selector Styles
  citySelector: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  citySelectorText: {
    fontSize: 16,
    color: '#0D3B66',
  },
  citySelectorPlaceholder: {
    color: '#8E9297',
  },
  // City Picker Modal Styles
  cityPickerContainer: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  cityPickerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  cityPickerCloseButton: {
    padding: 4,
  },
  cityPickerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#0D3B66',
  },
  cityPickerHeaderRight: {
    width: 32,
  },
  citySearchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginVertical: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  citySearchInput: {
    flex: 1,
    fontSize: 16,
    color: '#0D3B66',
    marginLeft: 8,
  },
  cityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F4',
  },
  cityItemText: {
    fontSize: 16,
    color: '#0D3B66',
  },
});

export default AddDriverModal;
