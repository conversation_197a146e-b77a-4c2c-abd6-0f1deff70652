// Stream polyfill for React Native
// This is a minimal polyfill for the Node.js stream module
// Fixed for Hermes engine compatibility

const EventEmitter = require('./events');

function Readable(options) {
  if (!(this instanceof Readable)) {
    return new Readable(options);
  }
  EventEmitter.call(this);
  this.readable = true;
  this.destroyed = false;
}

// Set up proper inheritance for Hermes
Readable.prototype = Object.create(EventEmitter.prototype);
Readable.prototype.constructor = Readable;

Object.assign(Readable.prototype, {
  read: function() {
    return null;
  },

  pipe: function(destination) {
    return destination;
  },

  destroy: function() {
    this.destroyed = true;
    this.emit('close');
  }
});

function Writable(options) {
  if (!(this instanceof Writable)) {
    return new Writable(options);
  }
  EventEmitter.call(this);
  this.writable = true;
  this.destroyed = false;
}

Writable.prototype = Object.create(EventEmitter.prototype);
Writable.prototype.constructor = Writable;

Object.assign(Writable.prototype, {
  write: function(chunk, encoding, callback) {
    if (typeof encoding === 'function') {
      callback = encoding;
      encoding = 'utf8';
    }
    if (callback) callback();
    return true;
  },

  end: function(chunk, encoding, callback) {
    if (typeof chunk === 'function') {
      callback = chunk;
      chunk = null;
      encoding = 'utf8';
    } else if (typeof encoding === 'function') {
      callback = encoding;
      encoding = 'utf8';
    }
    if (chunk) this.write(chunk, encoding);
    if (callback) callback();
    this.emit('finish');
  },

  destroy: function() {
    this.destroyed = true;
    this.emit('close');
  }
});

function Transform(options) {
  if (!(this instanceof Transform)) {
    return new Transform(options);
  }
  Readable.call(this, options);
  this.writable = true;
}

Transform.prototype = Object.create(Readable.prototype);
Transform.prototype.constructor = Transform;

Object.assign(Transform.prototype, {
  _transform: function(chunk, encoding, callback) {
    callback(null, chunk);
  },

  write: function(chunk, encoding, callback) {
    const self = this;
    this._transform(chunk, encoding, function(err, data) {
      if (err) {
        self.emit('error', err);
        return;
      }
      if (data) self.push(data);
      if (callback) callback();
    });
    return true;
  },

  end: function(chunk, encoding, callback) {
    if (chunk) this.write(chunk, encoding);
    this.push(null);
    if (callback) callback();
  }
});

module.exports = {
  Readable,
  Writable,
  Transform,
  Stream: Readable,
  PassThrough: Transform,
  pipeline: () => {},
  finished: () => {},
};
