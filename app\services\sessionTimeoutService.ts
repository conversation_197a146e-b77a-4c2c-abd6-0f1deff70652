import { AppState, AppStateStatus } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { APP_CONFIG } from '../constants/config';

// Session timeout için gerekli tipleri tanımlayalım
type SessionTimeoutOptions = {
  onWarning: () => void;
  onTimeout: () => void;
};

// AsyncStorage için key'ler
const LOGIN_TIME_KEY = '@session:loginTime';

class SessionTimeoutService {
  private options: SessionTimeoutOptions | null = null;
  private warningTimer: NodeJS.Timeout | null = null;
  private timeoutTimer: NodeJS.Timeout | null = null;
  private checkIntervalTimer: NodeJS.Timeout | null = null;
  private appStateSubscription: any = null;
  private isActive = false;
  private loginTime: number | null = null;
  private isWarningShown = false;
  private lastBackgroundTime: number | null = null;
  
  // Sabitler - dakika cinsinden
  private readonly TIMEOUT_MINUTES = APP_CONFIG.session.timeoutMinutes; // 10 dakika sonra otomatik logout
  private readonly WARNING_MINUTES = APP_CONFIG.session.warningMinutes; // 5 dakika sonra uyarı göster
  private readonly CHECK_INTERVAL_SECONDS = APP_CONFIG.session.checkIntervalSeconds; // 30 saniyede bir kontrol et

  // Session timeout'u başlat
  start(options: SessionTimeoutOptions) {
    const now = Date.now();
    console.log('SessionTimeoutService: 🚀 Yeni session timeout sistemi başlatılıyor');
    console.log(`SessionTimeoutService: ⚙️ Konfigürasyon - Timeout: ${this.TIMEOUT_MINUTES} dakika, Uyarı: ${this.WARNING_MINUTES} dakika sonra`);
    console.log(`SessionTimeoutService: 📅 Login zamanı: ${new Date(now).toISOString()}`);
    
    this.options = options;
    this.isActive = true;
    this.isWarningShown = false;
    this.loginTime = now;

    // Login zamanını AsyncStorage'a kaydet
    this.saveLoginTime(now);

    // AppState listener'ı kur (iOS uyumluluğu için kritik)
    this.setupAppStateListener();

    // Periyodik kontrol başlat
    this.startPeriodicCheck();

    console.log('SessionTimeoutService: ✅ Servis başarıyla başlatıldı');
  }

  // Session timeout'u durdur
  stop() {
    console.log('SessionTimeoutService: 🛑 Servis durduruluyor');
    this.isActive = false;
    this.clearAllTimers();

    // AppState listener'ı temizle
    this.removeAppStateListener();

    this.options = null;
    this.isWarningShown = false;
    this.loginTime = null;
    
    // Login zamanını temizle
    this.clearLoginTime();
    
    console.log('SessionTimeoutService: ✅ Servis başarıyla durduruldu');
  }

  // Login zamanını AsyncStorage'a kaydet
  private async saveLoginTime(time: number) {
    try {
      await AsyncStorage.setItem(LOGIN_TIME_KEY, time.toString());
      console.log(`SessionTimeoutService: 💾 Login zamanı kaydedildi: ${new Date(time).toISOString()}`);
    } catch (error) {
      console.error('SessionTimeoutService: Login zamanı kaydedilemedi:', error);
    }
  }

  // Login zamanını AsyncStorage'dan al
  private async getLoginTime(): Promise<number | null> {
    try {
      const timeStr = await AsyncStorage.getItem(LOGIN_TIME_KEY);
      return timeStr ? parseInt(timeStr, 10) : null;
    } catch (error) {
      console.error('SessionTimeoutService: Login zamanı alınamadı:', error);
      return null;
    }
  }

  // Login zamanını temizle
  private async clearLoginTime() {
    try {
      await AsyncStorage.removeItem(LOGIN_TIME_KEY);
      console.log('SessionTimeoutService: 🗑️ Login zamanı temizlendi');
    } catch (error) {
      console.error('SessionTimeoutService: Login zamanı temizlenemedi:', error);
    }
  }

  // Oturumu uzat (yeni login zamanı ayarla)
  extendSession() {
    if (!this.isActive) return;

    const now = Date.now();
    console.log('SessionTimeoutService: 🔄 Oturum uzatılıyor');
    console.log(`SessionTimeoutService: 📅 Yeni login zamanı: ${new Date(now).toISOString()}`);
    
    this.isWarningShown = false;
    this.loginTime = now;
    this.saveLoginTime(now);
    
    // Timer'ları yeniden başlat
    this.clearAllTimers();
    this.startPeriodicCheck();
  }

  // AppState değişikliklerini handle et
  private handleAppStateChange = (nextAppState: AppStateStatus) => {
    console.log(`SessionTimeoutService: App state changed from ${AppState.currentState} to ${nextAppState} at ${new Date().toISOString()}`);

    if (nextAppState === 'active') {
      // Arka plandan ön plana geçiş
      if (this.lastBackgroundTime !== null) {
        this.handleForegroundReturn();
      }
      this.lastBackgroundTime = null;
    } else if (nextAppState === 'background') {
      // Ön plandan arka plana geçiş
      this.lastBackgroundTime = Date.now();
      console.log(`SessionTimeoutService: App went to background at ${new Date(this.lastBackgroundTime).toISOString()}`);
    }
  };

  // Arka plandan ön plana dönüşü handle et
  private handleForegroundReturn() {
    if (!this.isActive || this.lastBackgroundTime === null) return;

    const now = Date.now();
    const backgroundDuration = (now - this.lastBackgroundTime) / 1000; // saniye cinsinden
    console.log(`SessionTimeoutService: 📱 iOS - App was in background for ${backgroundDuration.toFixed(1)} seconds, returned at ${new Date(now).toISOString()}`);

    // iOS'ta background'da geçen süreyi login time'a ekle
    if (this.loginTime) {
      this.loginTime += (backgroundDuration * 1000); // milisaniye cinsinden ekle
      this.saveLoginTime(this.loginTime);
      console.log(`SessionTimeoutService: 📱 iOS - Login time adjusted for background duration: ${new Date(this.loginTime).toISOString()}`);
    }

    // Timer'ları yeniden başlat (iOS'ta background'da durmuş olabilir)
    this.clearAllTimers();
    this.startPeriodicCheck();
    
    // Hemen bir kontrol yap
    this.checkSessionStatus();
  }

  // Kullanıcı aktivitesini kaydet
  private recordActivity() {
    if (!this.isActive) return;
    
    const now = Date.now();
    console.log(`SessionTimeoutService: 📝 Aktivite kaydedildi: ${new Date(now).toISOString()}`);
    
    // Session'ı uzat
    this.extendSession();
  }

  // Timer'ları sıfırla
  private resetTimers() {
    // Önceki timer'ları temizle
    if (this.warningTimer) {
      clearTimeout(this.warningTimer);
      this.warningTimer = null;
    }
    
    if (this.timeoutTimer) {
      clearTimeout(this.timeoutTimer);
      this.timeoutTimer = null;
    }

    // Warning timer'ı ayarla
    const warningDelay = (this.TIMEOUT_MINUTES - this.WARNING_MINUTES) * 60 * 1000;
    this.warningTimer = setTimeout(() => {
      console.log(`SessionTimeoutService: Warning timer triggered at ${new Date().toISOString()}`);
      this.handleWarning();
    }, warningDelay);

    // Timeout timer'ı ayarla
    const timeoutDelay = this.TIMEOUT_MINUTES * 60 * 1000;
    this.timeoutTimer = setTimeout(() => {
      console.log(`SessionTimeoutService: Timeout timer triggered at ${new Date().toISOString()}`);
      this.handleTimeout();
    }, timeoutDelay);

    console.log(`SessionTimeoutService: Timers reset - Warning in ${warningDelay/1000}s (${new Date(Date.now() + warningDelay).toISOString()}), Timeout in ${timeoutDelay/1000}s (${new Date(Date.now() + timeoutDelay).toISOString()})`);
  }

  // Periyodik kontrol başlat
  private startPeriodicCheck() {
    if (!this.isActive) return;

    const checkInterval = this.CHECK_INTERVAL_SECONDS * 1000;
    console.log(`SessionTimeoutService: ⏰ Periyodik kontrol başlatılıyor - her ${this.CHECK_INTERVAL_SECONDS} saniyede bir`);
    
    // İlk kontrolü hemen yap
    this.checkSessionStatus();
    
    // Periyodik kontrol timer'ını ayarla
    this.checkIntervalTimer = setInterval(() => {
      this.checkSessionStatus();
    }, checkInterval);
    
    console.log(`SessionTimeoutService: ✅ Periyodik kontrol ayarlandı`);
  }

  // Session durumunu kontrol et
  private async checkSessionStatus() {
    if (!this.isActive) {
      return;
    }

    const now = Date.now();
    
    try {
      // Login zamanını al (önce memory'den, sonra AsyncStorage'dan)
      let loginTime = this.loginTime;
      if (!loginTime) {
        loginTime = await this.getLoginTime();
        if (loginTime) {
          this.loginTime = loginTime;
        }
      }
      
      if (!loginTime) {
        console.log('SessionTimeoutService: ⚠️ Login zamanı bulunamadı, servis durduruluyor');
        this.stop();
        return;
      }

      const elapsedMinutes = (now - loginTime) / (1000 * 60);
      const elapsedSeconds = (now - loginTime) / 1000;
      
      // Detaylı log her kontrol sırasında
      console.log(`SessionTimeoutService: 🔍 KONTROL DETAYI [${new Date(now).toISOString()}]`);
      console.log(`  📅 Login zamanı: ${new Date(loginTime).toISOString()}`);
      console.log(`  ⏱️  Geçen süre: ${elapsedMinutes.toFixed(2)} dakika (${elapsedSeconds.toFixed(1)} saniye)`);
      console.log(`  ⚠️  Uyarı eşiği: ${this.WARNING_MINUTES} dakika`);
      console.log(`  ❌ Timeout eşiği: ${this.TIMEOUT_MINUTES} dakika`);
      
      // 5 dakika geçmişse otomatik logout
      if (elapsedMinutes >= this.TIMEOUT_MINUTES) {
        console.log(`SessionTimeoutService: 🚨 TIMEOUT! ${elapsedMinutes.toFixed(2)} dakika >= ${this.TIMEOUT_MINUTES} dakika`);
        console.log(`SessionTimeoutService: 🔄 Otomatik logout başlatılıyor...`);
        this.handleTimeout();
        return;
      }
      
      // 2 dakika geçmişse ve henüz uyarı gösterilmemişse uyarı göster
      if (elapsedMinutes >= this.WARNING_MINUTES && !this.isWarningShown) {
        console.log(`SessionTimeoutService: ⚠️ UYARI! ${elapsedMinutes.toFixed(2)} dakika >= ${this.WARNING_MINUTES} dakika`);
        console.log(`SessionTimeoutService: 📢 Kullanıcıya uyarı gösteriliyor...`);
        this.handleWarning();
        return;
      }
      
      // Normal durum
      const timeUntilWarning = this.WARNING_MINUTES - elapsedMinutes;
      const timeUntilTimeout = this.TIMEOUT_MINUTES - elapsedMinutes;
      
      if (elapsedMinutes < this.WARNING_MINUTES) {
        console.log(`  🎯 Durum: AKTIF - Uyarıya ${timeUntilWarning.toFixed(2)} dakika kaldı`);
      } else {
        console.log(`  🎯 Durum: UYARI GÖSTERİLDİ - Timeout'a ${timeUntilTimeout.toFixed(2)} dakika kaldı`);
      }
      
    } catch (error) {
      console.error('SessionTimeoutService: ❌ Session kontrol hatası:', error);
    }
  }

  // Warning'i handle et
  private handleWarning() {
    if (!this.isActive || !this.options) return;

    console.log(`SessionTimeoutService: 📢 Uyarı işleniyor - ${new Date().toISOString()}`);
    this.isWarningShown = true;
    this.options.onWarning();
  }

  // Timeout'u handle et
  private handleTimeout() {
    if (!this.isActive || !this.options) return;

    console.log(`SessionTimeoutService: 🚨 Timeout işleniyor - ${new Date().toISOString()}`);
    this.options.onTimeout();
    this.stop();
  }

  // AppState listener'ı kur
  private setupAppStateListener() {
    // Önceki listener'ı temizle
    this.removeAppStateListener();
    
    console.log('SessionTimeoutService: 📱 AppState listener kuruluyor (iOS uyumluluğu için)');
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);
  }

  // AppState listener'ı temizle
  private removeAppStateListener() {
    if (this.appStateSubscription) {
      console.log('SessionTimeoutService: 🧹 AppState listener temizleniyor');
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }
  }

  // Tüm timer'ları temizle
  private clearAllTimers() {
    if (this.warningTimer) {
      clearTimeout(this.warningTimer);
      this.warningTimer = null;
    }

    if (this.timeoutTimer) {
      clearTimeout(this.timeoutTimer);
      this.timeoutTimer = null;
    }

    if (this.checkIntervalTimer) {
      clearInterval(this.checkIntervalTimer);
      this.checkIntervalTimer = null;
    }

    console.log('SessionTimeoutService: 🧹 Tüm timer\'lar temizlendi');
  }


}

export const sessionTimeoutService = new SessionTimeoutService();
