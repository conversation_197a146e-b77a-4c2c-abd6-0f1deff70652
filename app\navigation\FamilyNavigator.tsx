import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Platform,
  StatusBar,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User } from '../services/authService';
import { useTranslation } from '../i18n/useTranslation';

// Import screens (will create these)
import FamilyDashboardScreen from '../screens/family/FamilyDashboardScreen';
import CaseTrackingScreen from '../screens/family/CaseTrackingScreen';
import FamilyDocumentsScreen from '../screens/family/FamilyDocumentsScreen';
import NotificationsScreen from '../screens/NotificationsScreen';
import ProfileScreen from '../screens/ProfileScreen';
import LanguageScreen from '../screens/LanguageScreen';

interface FamilyNavigatorProps {
  user: User;
  onLogout: () => void;
}

type FamilyScreen =
  | 'dashboard'
  | 'tracking'
  | 'documents'
  | 'notifications'
  | 'settings'
  | 'language'
  | 'profile';

const FamilyNavigator = ({ user, onLogout }: FamilyNavigatorProps) => {
  const { t } = useTranslation();
  const [currentScreen, setCurrentScreen] = useState<FamilyScreen>('dashboard');
  const [navigationStack, setNavigationStack] = useState<FamilyScreen[]>(['dashboard']);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [cameFromSettings, setCameFromSettings] = useState(false);

  // Navigation functions
  const navigateToScreen = (screen: FamilyScreen) => {
    if (screen === 'settings') {
      setShowSettingsModal(true);
      return;
    }
    setNavigationStack(prev => [...prev, screen]);
    setCurrentScreen(screen);
  };

  const navigateBack = () => {
    // If we came from settings and are on language or notifications, go back to settings
    if (cameFromSettings && (currentScreen === 'language' || currentScreen === 'notifications')) {
      setCameFromSettings(false);
      setCurrentScreen('dashboard');
      setShowSettingsModal(true);
      return;
    }

    if (navigationStack.length > 1) {
      const newStack = [...navigationStack];
      newStack.pop(); // Remove current screen
      const previousScreen = newStack[newStack.length - 1];
      setNavigationStack(newStack);
      setCurrentScreen(previousScreen);
    }
  };

  const menuItems = [
    { id: 'tracking', title: 'Vaka Takibi', icon: 'eye', color: '#0D3B66' },
    { id: 'documents', title: 'Belgeler', icon: 'document-text', color: '#9B59B6' },
    { id: 'settings', title: 'Ayarlar', icon: 'settings', color: '#8E9297' },
  ];

  const renderScreen = () => {
    switch (currentScreen) {
      case 'dashboard':
        return <FamilyDashboardScreen user={user} onNavigate={navigateToScreen} />;
      case 'tracking':
        return <CaseTrackingScreen user={user} onBack={navigateBack} />;
      case 'documents':
        return <FamilyDocumentsScreen user={user} onBack={navigateBack} />;
      case 'notifications':
        return <NotificationsScreen onBack={navigateBack} />;
      case 'language':
        return <LanguageScreen onBack={navigateBack} />;
      case 'profile':
        return <ProfileScreen user={user} onBack={navigateBack} onLogout={onLogout} />;
      default:
        return <FamilyDashboardScreen user={user} onNavigate={navigateToScreen} />;
    }
  };

  // If not on dashboard, show the selected screen
  if (currentScreen !== 'dashboard') {
    return renderScreen();
  }

  // Dashboard with navigation menu
  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <View style={styles.logoContainer}>
            <Ionicons name="home" size={24} color="#FFFFFF" />
          </View>
          <View>
            <Text style={styles.welcomeText}>{t(`roles.${user.role.toLowerCase()}`)}</Text>
            <Text style={styles.userName}>{user.full_name}</Text>
          </View>
        </View>
        <TouchableOpacity
          style={styles.profileButton}
          onPress={() => navigateToScreen('profile')}
        >
          <Ionicons name="person-circle" size={32} color="#0D3B66" />
        </TouchableOpacity>
      </View>

      {/* Current Case Status */}
      <View style={styles.currentCase}>
        <View style={styles.caseHeader}>
          <Text style={styles.caseTitle}>Mevcut Vaka Durumu</Text>
          <View style={styles.statusBadge}>
            <Text style={styles.statusText}>DEVAM EDİYOR</Text>
          </View>
        </View>
        <Text style={styles.caseName}>Ahmet Yılmaz - Cenaze Süreci</Text>
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: '60%' }]} />
          </View>
          <Text style={styles.progressText}>%60 Tamamlandı</Text>
        </View>
      </View>

      {/* Menu Grid */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Text style={styles.sectionTitle}>Hizmetler</Text>

        <View style={styles.menuList}>
          {menuItems.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={styles.menuItem}
              onPress={() => navigateToScreen(item.id as FamilyScreen)}
            >
              <View style={[styles.menuIcon, { backgroundColor: item.color }]}>
                <Ionicons name={item.icon as any} size={24} color="#FFFFFF" />
              </View>
              <View style={styles.menuContent}>
                <View style={styles.menuHeader}>
                  <Text style={styles.menuTitle}>{item.title}</Text>
                  {item.badge && (
                    <View style={styles.badge}>
                      <Text style={styles.badgeText}>{item.badge}</Text>
                    </View>
                  )}
                </View>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#8E9297" />
            </TouchableOpacity>
          ))}
        </View>

        {/* Support Info */}
        <View style={styles.supportInfo}>
          <Text style={styles.sectionTitle}>Destek</Text>
          <View style={styles.supportCard}>
            <View style={styles.supportHeader}>
              <Ionicons name="headset" size={24} color="#0D3B66" />
              <Text style={styles.supportTitle}>7/24 Destek Hattı</Text>
            </View>
            <Text style={styles.supportPhone}>0212 XXX XX XX</Text>
            <Text style={styles.supportDescription}>
              Herhangi bir sorunuz olduğunda bizi arayabilirsiniz.
            </Text>
          </View>
        </View>

        {/* Recent Updates */}
        <View style={styles.recentUpdates}>
          <Text style={styles.sectionTitle}>Son Güncellemeler</Text>
          <View style={styles.updateItem}>
            <View style={styles.updateIcon}>
              <Ionicons name="checkmark-circle" size={16} color="#2ECC71" />
            </View>
            <View style={styles.updateContent}>
              <Text style={styles.updateTitle}>Belge Onaylandı</Text>
              <Text style={styles.updateTime}>2 saat önce</Text>
            </View>
          </View>
          <View style={styles.updateItem}>
            <View style={styles.updateIcon}>
              <Ionicons name="time" size={16} color="#FAA916" />
            </View>
            <View style={styles.updateContent}>
              <Text style={styles.updateTitle}>Randevu Planlandı</Text>
              <Text style={styles.updateTime}>5 saat önce</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Settings Modal */}
      <Modal
        visible={showSettingsModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowSettingsModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          {/* Modal Header */}
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setShowSettingsModal(false)}
              style={styles.modalCloseButton}
            >
              <Ionicons name="close" size={24} color="#8E9297" />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Ayarlar</Text>
            <View style={styles.modalHeaderRight} />
          </View>

          {/* Modal Content */}
          <View style={styles.modalContent}>
            <Text style={styles.modalSectionTitle}>Uygulama Ayarları</Text>

            <View style={styles.settingsOptions}>
              <TouchableOpacity
                style={styles.settingsOption}
                onPress={() => {
                  setShowSettingsModal(false);
                  setCameFromSettings(true);
                  navigateToScreen('language');
                }}
              >
                <View style={styles.settingsOptionLeft}>
                  <View style={[styles.settingsOptionIcon, { backgroundColor: '#FAA916' }]}>
                    <Ionicons name="language" size={20} color="#FFFFFF" />
                  </View>
                  <View style={styles.settingsOptionInfo}>
                    <Text style={styles.settingsOptionTitle}>Dil</Text>
                    <Text style={styles.settingsOptionDescription}>Uygulama dilini değiştir</Text>
                  </View>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#8E9297" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.settingsOption}
                onPress={() => {
                  setShowSettingsModal(false);
                  setCameFromSettings(true);
                  navigateToScreen('notifications');
                }}
              >
                <View style={styles.settingsOptionLeft}>
                  <View style={[styles.settingsOptionIcon, { backgroundColor: '#2ECC71' }]}>
                    <Ionicons name="notifications" size={20} color="#FFFFFF" />
                  </View>
                  <View style={styles.settingsOptionInfo}>
                    <Text style={styles.settingsOptionTitle}>Bildirimler</Text>
                    <Text style={styles.settingsOptionDescription}>Bildirim ayarlarını yönet</Text>
                  </View>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#8E9297" />
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
    backgroundColor: '#FFFFFF',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#9B59B6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  welcomeText: {
    fontSize: 14,
    color: '#8E9297',
  },
  userName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  profileButton: {
    padding: 4,
  },
  roleBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F3E5F5',
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginHorizontal: 24,
    marginTop: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#9B59B6',
  },
  roleText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#4A148C',
    marginLeft: 4,
  },
  currentCase: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 24,
    marginTop: 16,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  caseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  caseTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8E9297',
  },
  statusBadge: {
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#1B5E20',
  },
  caseName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 16,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: '#E1E8ED',
    borderRadius: 3,
    marginRight: 12,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#2ECC71',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#2ECC71',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginTop: 24,
    marginBottom: 16,
  },
  menuList: {
    marginBottom: 8,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  menuIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  menuContent: {
    flex: 1,
  },
  menuHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    flex: 1,
  },
  badge: {
    backgroundColor: '#E74C3C',
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
    marginLeft: 8,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  menuDescription: {
    fontSize: 14,
    color: '#8E9297',
  },
  supportInfo: {
    marginTop: 8,
  },
  supportCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  supportHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  supportTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginLeft: 12,
  },
  supportPhone: {
    fontSize: 18,
    fontWeight: '700',
    color: '#0D3B66',
    marginBottom: 8,
  },
  supportDescription: {
    fontSize: 14,
    color: '#8E9297',
  },
  recentUpdates: {
    marginTop: 8,
    marginBottom: 32,
  },
  updateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  updateIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  updateContent: {
    flex: 1,
  },
  updateTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  updateTime: {
    fontSize: 12,
    color: '#8E9297',
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  modalCloseButton: {
    padding: 4,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  modalHeaderRight: {
    width: 32,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 24,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 16,
  },
  settingsOptions: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    overflow: 'hidden',
  },
  settingsOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingsOptionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingsOptionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  settingsOptionInfo: {
    flex: 1,
  },
  settingsOptionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  settingsOptionDescription: {
    fontSize: 14,
    color: '#8E9297',
  },
});

export default FamilyNavigator;
