import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import ScreenLayout from '../components/ScreenLayout';
import { useTranslation } from 'react-i18next';

interface CaseItem {
  id: string;
  status: string;
  burial_type: string;
  family_user_id: string;
  created_at: string;
  deceased: {
    id: string;
    full_name: string;
    date_of_death: string;
    nationality: string;
    family_name: string;
    family_email: string;
    family_phone: string;
  } | null;
}

interface CaseListScreenProps {
  onLogout?: () => void;
  navigation?: any;
}

const CaseListScreen = ({ onLogout, navigation }: CaseListScreenProps) => {
  const { t } = useTranslation();
  const [cases, setCases] = useState<CaseItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCases();
  }, []);

  const fetchCases = async () => {
    try {
      console.log('CaseListScreen: Fetching cases from Supabase...');
      const { data, error } = await supabase
        .from('cases')
        .select(`
          id,
          status,
          burial_type,
          family_user_id,
          created_at,
          deceased:deceased_id (
            id,
            full_name,
            date_of_death,
            nationality,
            family_name,
            family_email,
            family_phone
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.log('CaseListScreen: Error fetching cases:', error.message);
        return;
      }

      console.log('CaseListScreen: Cases fetched successfully:', data?.length);
      setCases(data || []);
    } catch (error) {
      console.log('CaseListScreen: Exception while fetching cases:', error);
    } finally {
      setLoading(false);
    }
  };
  const renderCaseItem = ({ item }: { item: CaseItem }) => {
    const fullName = item.deceased?.full_name || 'Bilinmeyen';
    const formattedDate = item.deceased?.date_of_death
      ? new Date(item.deceased.date_of_death).toLocaleDateString('tr-TR')
      : new Date(item.created_at).toLocaleDateString('tr-TR');
    const statusText = getStatusText(item.status);
    const burialTypeText = item.burial_type === 'DE' ? '🇩🇪 Almanya' : item.burial_type === 'TR' ? '🇹🇷 Türkiye' : '';

    return (
      <TouchableOpacity
        style={styles.caseCard}
        onPress={() => {
          if (navigation?.navigate) {
            navigation.navigate('caseDetail', item);
          }
        }}
      >
        <View style={styles.caseHeader}>
          <Text style={styles.deceasedName}>{fullName}</Text>
          <View style={[styles.statusBadge, getStatusStyle(item.status)]}>
            <Text style={[styles.statusText, getStatusTextStyle(item.status)]}>
              {statusText}
            </Text>
          </View>
        </View>
        <View style={styles.caseDetails}>
          <View style={styles.caseDetailsColumn}>
            <Text style={styles.detailText}>📅 {formattedDate}</Text>
            {burialTypeText && <Text style={styles.detailText}>{burialTypeText}</Text>}
          </View>
          {item.deceased?.family_name && (
            <View style={styles.caseDetailsColumn}>
              <Text style={styles.detailText}>👨‍👩‍👧‍👦 {item.deceased.family_name}</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'OPEN':
        return 'Açık';
      case 'CLOSED':
        return 'Kapalı';
      case 'CANCELLED':
        return 'İptal Edildi';
      default:
        return status;
    }
  };

  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'CLOSED':
        return { backgroundColor: '#E8F5E8' };
      case 'OPEN':
        return { backgroundColor: '#E3F2FD' };
      case 'CANCELLED':
        return { backgroundColor: '#F8D7DA' };
      default:
        return { backgroundColor: '#E3F2FD' };
    }
  };

  const getStatusTextStyle = (status: string) => {
    switch (status) {
      case 'CLOSED':
        return { color: '#2ECC71' };
      case 'OPEN':
        return { color: '#2196F3' };
      case 'CANCELLED':
        return { color: '#E74C3C' };
      default:
        return { color: '#2196F3' };
    }
  };

  const screenWidth = Dimensions.get('window').width;
  const isSmallScreen = screenWidth < 400;

  const headerRightComponent = onLogout ? (
    <TouchableOpacity
      style={[styles.logoutButton, isSmallScreen && styles.logoutButtonSmall]}
      onPress={onLogout}
    >
      <Ionicons name="log-out" size={16} color="#FFFFFF" />
      {!isSmallScreen && <Text style={styles.logoutButtonText}>Çıkış</Text>}
    </TouchableOpacity>
  ) : undefined;

  return (
    <ScreenLayout
      title={isSmallScreen ? t('navigation.cases') : t('cases.allCases')}
      showBackButton
      onBack={() => navigation?.goBack && navigation.goBack()}
      rightComponent={headerRightComponent}
      contentPadding={false}
    >
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0D3B66" />
          <Text style={styles.loadingText}>{t('cases.loadingCases')}</Text>
        </View>
      ) : (
        <FlatList
          data={cases}
          renderItem={renderCaseItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshing={loading}
          onRefresh={fetchCases}
        />
      )}
    </ScreenLayout>
  );
};

const styles = StyleSheet.create({
  logoutButton: {
    backgroundColor: '#E74C3C',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 6,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  logoutButtonSmall: {
    paddingHorizontal: 6,
  },
  logoutButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  listContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#8E9297',
  },
  caseCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  caseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  deceasedName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  caseDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  caseDetailsColumn: {
    flexDirection: 'column',
    gap: 4,
  },
  detailText: {
    fontSize: 14,
    color: '#8E9297',
  },
});

export default CaseListScreen;
