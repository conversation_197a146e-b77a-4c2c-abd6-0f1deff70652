import { APP_CONFIG } from '../constants/config';

/**
 * Email yönetimi için yardımcı servis
 * Hardcode edilmiş email adreslerini önlemek ve merkezi yönetim sağlamak için kullanılır
 */
export class EmailService {
  /**
   * Varsayılan email adreslerini döndürür
   */
  static getDefaultEmails() {
    return APP_CONFIG.emails.default;
  }

  /**
   * <PERSON><PERSON><PERSON> email adreslerini döndürür
   */
  static getDepartmentEmails() {
    return APP_CONFIG.emails.departments;
  }

  /**
   * İletişim kişilerini döndürür
   */
  static getContacts() {
    return APP_CONFIG.emails.contacts;
  }

  /**
   * <PERSON><PERSON><PERSON> bir departman için email adresi döndürür
   * @param department - Departman adı (driver, support, admin)
   * @returns Email adresi
   */
  static getEmailByDepartment(department: 'driver' | 'support' | 'admin'): string {
    switch (department) {
      case 'driver':
        return APP_CONFIG.emails.departments.driver;
      case 'support':
        return APP_CONFIG.emails.default.support;
      case 'admin':
        return APP_CONFIG.emails.default.admin;
      default:
        return APP_CONFIG.emails.default.support;
    }
  }

  /**
   * Cami email adresi döndürür
   * @param mosque - Cami adı (fatih, merkez)
   * @returns Email adresi
   */
  static getMosqueEmail(mosque: 'fatih' | 'merkez' = 'fatih'): string {
    return APP_CONFIG.emails.departments.mosque[mosque];
  }

  /**
   * Mezarlık email adresi döndürür
   * @param cemetery - Mezarlık adı (karacaahmet, merkez)
   * @returns Email adresi
   */
  static getCemeteryEmail(cemetery: 'karacaahmet' | 'merkez' = 'karacaahmet'): string {
    return APP_CONFIG.emails.departments.cemetery[cemetery];
  }

  /**
   * Fallback email adresi döndürür (family_email boş olduğunda kullanılır)
   * @returns Varsayılan aile email adresi
   */
  static getFallbackFamilyEmail(): string {
    return APP_CONFIG.emails.default.family;
  }

  /**
   * UI placeholder email adresi döndürür
   * @returns Placeholder email adresi
   */
  static getPlaceholderEmail(): string {
    return APP_CONFIG.ui.placeholders.email;
  }

  /**
   * Email adresinin geçerli olup olmadığını kontrol eder
   * @param email - Kontrol edilecek email adresi
   * @returns Geçerli ise true, değilse false
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Email adresini formatlar (küçük harf, boşlukları temizler)
   * @param email - Formatlanacak email adresi
   * @returns Formatlanmış email adresi
   */
  static formatEmail(email: string): string {
    return email.trim().toLowerCase();
  }
}

// Kolay kullanım için export
export const emailService = EmailService;