import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ActivityIndicator,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { authService } from '../services/authService';
import { useAlertHelpers } from '../hooks/useAlert';
import { useTranslation } from '../i18n/useTranslation';
import { emailService } from '../services/emailService';
import { VALIDATION_RULES } from '../constants/config';

interface FirstTimeLoginScreenProps {
  onSuccess: () => void;
  onBack: () => void;
}

const FirstTimeLoginScreen = ({ onSuccess, onBack }: FirstTimeLoginScreenProps) => {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'email' | 'password'>('email');
  const [invitation, setInvitation] = useState<any>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const { showError, showInfo, showSuccess } = useAlertHelpers();

  const handleCheckStaff = async () => {
    if (!email.trim()) {
      showError(t('common.error'), t('alerts.enterEmail'));
      return;
    }

    setLoading(true);
    try {
      const result = await authService.checkUserByEmail(email);

      if (result.success && result.invitation) {
        setInvitation(result.invitation);
        setStep('password');
      } else {
        const errorMessage = result.error || result.message || 'Bu e-posta adresi sistemde kayıtlı değil.';

        // Merkezi sistem ile uyumlu hata mesajları
        if (errorMessage.includes('zaten hesap oluşturulmuş') ||
            errorMessage.includes('zaten hesap var') ||
            errorMessage.includes('zaten şifre belirlenmiş')) {
          // Kullanıcı zaten auth.users'da var - giriş sayfasına yönlendir
          showInfo(
            'Hesap Zaten Mevcut',
            'Bu e-posta adresi için zaten hesap oluşturulmuş. Lütfen giriş sayfasını kullanın.',
            () => onBack()
          );
        } else if (errorMessage.includes('emailNotRegistered') || 
                   errorMessage.includes('kayıtlı değil') ||
                   errorMessage.includes('Email not registered')) {
          // Kullanıcı users tablosunda yok
          showError('Kayıt Bulunamadı', 'Bu e-posta adresi sistemde kayıtlı değil.');
        } else {
          // Diğer durumlar için genel hata mesajı
          showError('Hata', errorMessage);
        }
      }
    } catch (error) {
      showError(t('common.error'), t('alerts.checkFailed'));
    } finally {
      setLoading(false);
    }
  };

  const validatePassword = (pwd: string) => {
    const minLength = pwd.length >= VALIDATION_RULES.password.minLength.value;
    const hasNumber = /\d/.test(pwd);
    const hasLetter = /[a-zA-Z]/.test(pwd);

    return {
      minLength,
      hasNumber,
      hasLetter,
      isValid: minLength && hasNumber && hasLetter
    };
  };

  const handleSetPassword = async () => {
    if (!password.trim()) {
      showError(t('common.error'), t('alerts.enterPassword'));
      return;
    }

    if (password !== confirmPassword) {
      showError(t('common.error'), t('auth.passwordMismatch'));
      return;
    }

    const validation = validatePassword(password);
    if (!validation.isValid) {
      let errorMessage = 'Şifre gereksinimleri:';
      if (!validation.minLength) {
        errorMessage += `\n• En az ${VALIDATION_RULES.password.minLength.value} karakter`;
      }
      if (!validation.hasLetter) {
        errorMessage += '\n• En az bir harf';
      }
      if (!validation.hasNumber) {
        errorMessage += '\n• En az bir rakam';
      }
      showError(t('common.error'), errorMessage);
      return;
    }

    setLoading(true);
    try {
      const result = await authService.createUserWithPassword(email, password);

      if (result.success) {
        showSuccess(
          t('common.success'),
          t('alerts.accountCreatedSuccessfully'),
          onSuccess
        );
      } else {
        showInfo(t('common.info'), result.message || t('alerts.accountCreationFailed'));
      }
    } catch (error) {
      showError(t('common.error'), t('alerts.unexpectedErrorTryAgain'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBack}>
            <Ionicons name="arrow-back" size={24} color="#1C1C1E" />
          </TouchableOpacity>
          <Text style={styles.title}>Hesap Oluştur</Text>
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.form}>
          {step === 'email' ? (
            <>
              <Text style={styles.description}>
                E-posta adresinizi girin. Sistemde kayıtlı personel için hesap oluşturabilirsiniz.
              </Text>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>E-posta Adresi</Text>
                <TextInput
                  style={styles.input}
                  value={email}
                  onChangeText={setEmail}
                  placeholder={emailService.getPlaceholderEmail()}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  autoComplete="off"
                  editable={!loading}
                />
              </View>

              <TouchableOpacity
                style={[styles.button, loading && styles.buttonDisabled]}
                onPress={handleCheckStaff}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  <Text style={styles.buttonText}>Devam Et</Text>
                )}
              </TouchableOpacity>
            </>
          ) : (
            <>
              <Text style={styles.description}>
                Merhaba! Lütfen hesabınız için bir şifre belirleyin.
              </Text>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>E-posta Adresi</Text>
                <TextInput
                  style={[styles.input, styles.inputDisabled]}
                  value={email}
                  editable={false}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>Yeni Şifre</Text>
                <View style={styles.passwordInputContainer}>
                  <TextInput
                    style={styles.passwordInput}
                    value={password}
                    onChangeText={setPassword}
                    placeholder={t('auth.minSixChars')}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                    autoComplete="off"
                    editable={!loading}
                  />
                  <TouchableOpacity
                    style={styles.eyeButton}
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    <Ionicons
                      name={showPassword ? 'eye-off' : 'eye'}
                      size={20}
                      color="#8E9297"
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Password Requirements */}
              <View style={styles.requirementsContainer}>
                <Text style={styles.requirementsTitle}>{t('auth.passwordRequirementsTitle')}</Text>

                <View style={styles.requirement}>
                  <Ionicons
                    name={validatePassword(password).minLength ? 'checkmark-circle' : 'ellipse-outline'}
                    size={16}
                    color={validatePassword(password).minLength ? '#2ECC71' : '#8E9297'}
                  />
                  <Text style={[
                    styles.requirementText,
                    validatePassword(password).minLength && styles.requirementMet
                  ]}>
                    {t('auth.minSixChars')}
                  </Text>
                </View>

                <View style={styles.requirement}>
                  <Ionicons
                    name={validatePassword(password).hasLetter ? 'checkmark-circle' : 'ellipse-outline'}
                    size={16}
                    color={validatePassword(password).hasLetter ? '#2ECC71' : '#8E9297'}
                  />
                  <Text style={[
                    styles.requirementText,
                    validatePassword(password).hasLetter && styles.requirementMet
                  ]}>
                    {t('auth.atLeastOneLetter')}
                  </Text>
                </View>

                <View style={styles.requirement}>
                  <Ionicons
                    name={validatePassword(password).hasNumber ? 'checkmark-circle' : 'ellipse-outline'}
                    size={16}
                    color={validatePassword(password).hasNumber ? '#2ECC71' : '#8E9297'}
                  />
                  <Text style={[
                    styles.requirementText,
                    validatePassword(password).hasNumber && styles.requirementMet
                  ]}>
                    {t('auth.atLeastOneNumber')}
                  </Text>
                </View>
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>{t('auth.confirmPassword')}</Text>
                <View style={styles.passwordInputContainer}>
                  <TextInput
                    style={styles.passwordInput}
                    value={confirmPassword}
                    onChangeText={setConfirmPassword}
                    placeholder={t('auth.enterPasswordAgain')}
                    secureTextEntry={!showConfirmPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                    autoComplete="off"
                    editable={!loading}
                  />
                  <TouchableOpacity
                    style={styles.eyeButton}
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    <Ionicons
                      name={showConfirmPassword ? 'eye-off' : 'eye'}
                      size={20}
                      color="#8E9297"
                    />
                  </TouchableOpacity>
                </View>
              </View>

              <TouchableOpacity
                style={[styles.button, loading && styles.buttonDisabled]}
                onPress={handleSetPassword}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  <Text style={styles.buttonText}>Şifreyi Belirle</Text>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.backToEmailButton}
                onPress={() => setStep('email')}
                disabled={loading}
              >
                <Text style={styles.backToEmailButtonText}>← E-posta Değiştir</Text>
              </TouchableOpacity>
            </>
          )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  keyboardView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#F5F8FA',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingBottom: 50,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  form: {
    marginTop: 40,
  },
  description: {
    fontSize: 16,
    color: '#8E9297',
    lineHeight: 24,
    marginBottom: 32,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  input: {
    height: 56,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    color: '#1C1C1E',
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  button: {
    height: 56,
    backgroundColor: '#0D3B66',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  buttonDisabled: {
    backgroundColor: '#8E9297',
  },
  inputDisabled: {
    backgroundColor: '#F5F8FA',
    color: '#8E9297',
  },
  backToEmailButton: {
    marginTop: 16,
    alignItems: 'center',
  },
  backToEmailButtonText: {
    color: '#0D3B66',
    fontSize: 14,
    fontWeight: '500',
  },
  passwordInputContainer: {
    position: 'relative',
  },
  passwordInput: {
    height: 56,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingRight: 50,
    fontSize: 16,
    color: '#1C1C1E',
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  eyeButton: {
    position: 'absolute',
    right: 16,
    top: 18,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  requirementsContainer: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  requirementsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 12,
  },
  requirement: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  requirementText: {
    fontSize: 14,
    color: '#8E9297',
    marginLeft: 8,
    flex: 1,
  },
  requirementMet: {
    color: '#2ECC71',
    fontWeight: '500',
  },
});

export default FirstTimeLoginScreen;
