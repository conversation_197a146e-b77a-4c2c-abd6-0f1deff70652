
import React from 'react';

const CaseListScreen = () => {
  return (
    <div className="min-h-screen bg-[#F5F8FA]">
      {/* Header */}
      <div className="bg-white border-b border-[#E1E8ED] px-4 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold text-[#1C1C1E]">Vakalar</h1>
          <div className="w-8 h-8 bg-[#0D3B66] rounded-full"></div>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="bg-white px-4 py-3 border-b border-[#E1E8ED]">
        <div className="flex space-x-4">
          <button className="px-4 py-2 bg-[#0D3B66] text-white rounded-lg text-sm font-medium">
            A<PERSON><PERSON>k (3)
          </button>
          <button className="px-4 py-2 bg-[#E1E8ED] text-[#8E9297] rounded-lg text-sm font-medium">
            <PERSON><PERSON><PERSON> (12)
          </button>
        </div>
      </div>

      {/* Case List */}
      <div className="p-4 space-y-3">
        {/* Active Case 1 */}
        <div className="bg-white rounded-lg border border-[#E1E8ED] p-4">
          <div className="flex items-start justify-between mb-2">
            <div>
              <h3 className="font-medium text-[#1C1C1E]">Vaka #2024-001</h3>
              <p className="text-sm text-[#8E9297]">Ahmet Yılmaz</p>
            </div>
            <div className="px-2 py-1 bg-[#FAA916] text-white rounded text-xs font-medium">
              Devam Ediyor
            </div>
          </div>
          <p className="text-xs text-[#8E9297] mb-3">15 Aralık 2024, 14:30</p>
          <div className="flex items-center justify-between">
            <div className="text-xs text-[#8E9297]">3/8 görev tamamlandı</div>
            <div className="w-16 h-1 bg-[#E1E8ED] rounded-full">
              <div className="w-6 h-1 bg-[#0D3B66] rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Active Case 2 */}
        <div className="bg-white rounded-lg border border-[#E1E8ED] p-4">
          <div className="flex items-start justify-between mb-2">
            <div>
              <h3 className="font-medium text-[#1C1C1E]">Vaka #2024-002</h3>
              <p className="text-sm text-[#8E9297]">Fatma Demir</p>
            </div>
            <div className="px-2 py-1 bg-[#E74C3C] text-white rounded text-xs font-medium">
              Acil
            </div>
          </div>
          <p className="text-xs text-[#8E9297] mb-3">15 Aralık 2024, 16:45</p>
          <div className="flex items-center justify-between">
            <div className="text-xs text-[#8E9297]">1/8 görev tamamlandı</div>
            <div className="w-16 h-1 bg-[#E1E8ED] rounded-full">
              <div className="w-2 h-1 bg-[#E74C3C] rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Active Case 3 */}
        <div className="bg-white rounded-lg border border-[#E1E8ED] p-4">
          <div className="flex items-start justify-between mb-2">
            <div>
              <h3 className="font-medium text-[#1C1C1E]">Vaka #2024-003</h3>
              <p className="text-sm text-[#8E9297]">Mehmet Kaya</p>
            </div>
            <div className="px-2 py-1 bg-[#2ECC71] text-white rounded text-xs font-medium">
              Normal
            </div>
          </div>
          <p className="text-xs text-[#8E9297] mb-3">16 Aralık 2024, 09:15</p>
          <div className="flex items-center justify-between">
            <div className="text-xs text-[#8E9297]">6/8 görev tamamlandı</div>
            <div className="w-16 h-1 bg-[#E1E8ED] rounded-full">
              <div className="w-12 h-1 bg-[#2ECC71] rounded-full"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-[#E1E8ED] px-4 py-3">
        <div className="flex justify-around">
          <div className="flex flex-col items-center space-y-1">
            <div className="w-6 h-6 bg-[#0D3B66] rounded"></div>
            <span className="text-xs text-[#0D3B66] font-medium">Vakalar</span>
          </div>
          <div className="flex flex-col items-center space-y-1">
            <div className="w-6 h-6 bg-[#8E9297] rounded"></div>
            <span className="text-xs text-[#8E9297]">Görevler</span>
          </div>
          <div className="flex flex-col items-center space-y-1">
            <div className="w-6 h-6 bg-[#8E9297] rounded"></div>
            <span className="text-xs text-[#8E9297]">Ayarlar</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CaseListScreen;
