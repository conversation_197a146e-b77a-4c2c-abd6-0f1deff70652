import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface ScreenLayoutProps {
  children: React.ReactNode;
  title?: string;
  showBackButton?: boolean;
  onBack?: () => void;
  rightComponent?: React.ReactNode;
  scrollable?: boolean;
  backgroundColor?: string;
  headerBackgroundColor?: string;
  showStatusBar?: boolean;
  statusBarStyle?: 'default' | 'light-content' | 'dark-content';
  contentPadding?: boolean;
}

const ScreenLayout: React.FC<ScreenLayoutProps> = ({
  children,
  title,
  showBackButton = false,
  onBack,
  rightComponent,
  scrollable = false,
  backgroundColor = '#F5F8FA',
  headerBackgroundColor = '#FFFFFF',
  showStatusBar = true,
  statusBarStyle = 'dark-content',
  contentPadding = true,
}) => {
  const ContentWrapper = scrollable ? ScrollView : View;
  const contentWrapperProps = scrollable
    ? {
        showsVerticalScrollIndicator: false,
        contentContainerStyle: [
          styles.scrollContent,
          contentPadding && styles.contentPadding
        ]
      }
    : {
        style: [
          styles.content,
          contentPadding && styles.contentPadding
        ]
      };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      {showStatusBar && (
        <StatusBar
          barStyle={statusBarStyle}
          backgroundColor={Platform.OS === 'android' ? headerBackgroundColor : undefined}
        />
      )}

      {/* Header */}
      {(title || showBackButton || rightComponent) && (
        <View style={[styles.header, { backgroundColor: headerBackgroundColor }]}>
          <View style={styles.headerLeft}>
            {showBackButton && (
              <TouchableOpacity
                style={styles.backButton}
                onPress={onBack}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="arrow-back" size={24} color="#1C1C1E" />
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.headerCenter}>
            {title && (
              <Text style={styles.headerTitle} numberOfLines={1}>
                {title}
              </Text>
            )}
          </View>

          <View style={styles.headerRight}>
            {rightComponent}
          </View>
        </View>
      )}

      {/* Content */}
      <ContentWrapper {...contentWrapperProps}>
        {children}
      </ContentWrapper>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
    minHeight: 56,
  },
  headerLeft: {
    flex: 1,
    alignItems: 'flex-start',
  },
  headerCenter: {
    flex: 2,
    alignItems: 'center',
  },
  headerRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  contentPadding: {
    padding: 16,
  },
});

export default ScreenLayout;
