import { NavigationContainerRef } from '@react-navigation/native';
import { useAuthStore } from '../store/authStore';

export class NavigationActivityTracker {
  private static instance: NavigationActivityTracker;
  private navigationRef: NavigationContainerRef<any> | null = null;

  static getInstance(): NavigationActivityTracker {
    if (!NavigationActivityTracker.instance) {
      NavigationActivityTracker.instance = new NavigationActivityTracker();
    }
    return NavigationActivityTracker.instance;
  }

  setNavigationRef(ref: NavigationContainerRef<any>): void {
    this.navigationRef = ref;
    this.setupNavigationListeners();
  }

  private setupNavigationListeners(): void {
    if (!this.navigationRef) return;

    // Listen to navigation state changes
    this.navigationRef.addListener('state', () => {
      this.recordActivity();
    });

    // Listen to navigation actions (alternative to focus events)
    this.navigationRef.addListener('__unsafe_action__', () => {
      this.recordActivity();
    });
  }

  private recordActivity(): void {
    try {
      const authStore = useAuthStore.getState();
      if (authStore.isAuthenticated) {
        // recordUserActivity artık kullanılmıyor - yeni session timeout sistemi login zamanına dayalı
        console.log('NavigationActivityTracker: Navigation activity detected');
      }
    } catch (error) {
      console.error('NavigationActivityTracker: Error recording activity:', error);
    }
  }
}

export const navigationActivityTracker = NavigationActivityTracker.getInstance();
