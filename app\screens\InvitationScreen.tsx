import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  ActivityIndicator,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import { authService } from '../services/authService';
import { useTranslation } from 'react-i18next';
import { VALIDATION_RULES } from '../constants/config';

interface InvitationScreenProps {
  token: string;
  onSuccess: () => void;
  onBack: () => void;
}

const InvitationScreen = ({ token, onSuccess, onBack }: InvitationScreenProps) => {
  const { t } = useTranslation();
  const [invitation, setInvitation] = useState<any>(null);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(true);
  const [accepting, setAccepting] = useState(false);

  useEffect(() => {
    fetchInvitation();
  }, []);

  const fetchInvitation = async () => {
    try {
      console.log('InvitationScreen: Fetching invitation with token:', token);

      const { data, error } = await supabase
        .from('invitations')
        .select('*')
        .eq('token', token)
        .eq('status', 'pending')
        .gt('expires_at', new Date().toISOString())
        .single();

      if (error || !data) {
        console.log('InvitationScreen: Invalid invitation:', error?.message);
        Alert.alert(
          t('alerts.invalidInvitation'),
          t('alerts.invalidInvitationMessage'),
          [{ text: t('common.ok'), onPress: onBack }]
        );
        return;
      }

      console.log('InvitationScreen: Invitation found:', data);
      setInvitation(data);
    } catch (error: any) {
      console.log('InvitationScreen: Exception:', error.message);
      Alert.alert(t('common.error'), t('alerts.invitationCheckFailed'));
    } finally {
      setLoading(false);
    }
  };

  const validatePassword = (pwd: string) => {
    const minLength = pwd.length >= VALIDATION_RULES.password.minLength.value;
    const hasNumber = /\d/.test(pwd);
    const hasLetter = /[a-zA-Z]/.test(pwd);

    return {
      minLength,
      hasNumber,
      hasLetter,
      isValid: minLength && hasNumber && hasLetter
    };
  };

  const handleAcceptInvitation = async () => {
    if (!password.trim() || !confirmPassword.trim()) {
      Alert.alert(t('common.error'), t('alerts.fillAllFields'));
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert(t('common.error'), t('alerts.passwordMismatch'));
      return;
    }

    const validation = validatePassword(password);
    if (!validation.isValid) {
      Alert.alert(
        t('alerts.invalidPassword'),
        t('alerts.passwordRequirements')
      );
      return;
    }

    setAccepting(true);
    try {
      console.log('InvitationScreen: Accepting invitation...');

      const { data, error } = await supabase.rpc('accept_invitation', {
        p_token: token,
        p_password: password
      });

      if (error) {
        console.log('InvitationScreen: Accept error:', error.message);
        Alert.alert(t('common.error'), t('alerts.invitationAcceptFailed'));
        return;
      }

      console.log('InvitationScreen: Invitation accepted successfully');
      Alert.alert(
        t('common.success'),
        t('alerts.accountCreatedSuccess'),
        [{ text: t('common.ok'), onPress: onSuccess }]
      );

    } catch (error: any) {
      console.log('InvitationScreen: Exception:', error.message);
      Alert.alert(t('common.error'), t('alerts.unexpectedError'));
    } finally {
      setAccepting(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0D3B66" />
            <Text style={styles.loadingText}>{t('invitation.checkingInvitation')}</Text>
          </View>
        </SafeAreaView>
      </View>
    );
  }

  if (!invitation) {
    return (
      <View style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle" size={64} color="#E74C3C" />
            <Text style={styles.errorTitle}>{t('alerts.invalidInvitation')}</Text>
            <Text style={styles.errorText}>
              {t('alerts.invalidInvitationMessage')}
            </Text>
            <TouchableOpacity style={styles.backButton} onPress={onBack}>
              <Text style={styles.backButtonText}>{t('common.back')}</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </View>
    );
  }

  const validation = validatePassword(password);

  return (
    <View style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={onBack} style={styles.headerBackButton}>
              <Ionicons name="arrow-back" size={24} color="#0D3B66" />
            </TouchableOpacity>
            <Text style={styles.title}>{t('invitation.title')}</Text>
          </View>

          {/* Invitation Info */}
          <View style={styles.invitationInfo}>
            <Text style={styles.welcomeText}>{t('invitation.welcome')}</Text>
            <Text style={styles.nameText}>{invitation.name}</Text>
            <Text style={styles.emailText}>{invitation.email}</Text>
            <Text style={styles.roleText}>{t('invitation.role')}: {invitation.role}</Text>
          </View>

          {/* Form */}
          <View style={styles.form}>
            <Text style={styles.formTitle}>{t('invitation.createAccount')}</Text>
            <Text style={styles.formDescription}>
              {t('invitation.welcomeMessage')}
            </Text>

            {/* Password Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>{t('auth.password')}</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={styles.passwordInput}
                  value={password}
                  onChangeText={setPassword}
                  placeholder={t('auth.enterPassword')}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={styles.eyeButton}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Ionicons
                    name={showPassword ? 'eye-off' : 'eye'}
                    size={20}
                    color="#8E9297"
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Confirm Password Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>{t('auth.confirmPassword')}</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={styles.passwordInput}
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  placeholder={t('auth.enterPasswordAgain')}
                  secureTextEntry={!showConfirmPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={styles.eyeButton}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  <Ionicons
                    name={showConfirmPassword ? 'eye-off' : 'eye'}
                    size={20}
                    color="#8E9297"
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Password Requirements */}
            <View style={styles.requirementsContainer}>
              <Text style={styles.requirementsTitle}>{t('auth.passwordRequirementsTitle')}:</Text>

              <View style={styles.requirement}>
                <Ionicons
                  name={validation.minLength ? 'checkmark-circle' : 'ellipse-outline'}
                  size={16}
                  color={validation.minLength ? '#2ECC71' : '#8E9297'}
                />
                <Text style={[
                  styles.requirementText,
                  validation.minLength && styles.requirementMet
                ]}>                  {t('auth.minSixChars')}
                </Text>
              </View>

              <View style={styles.requirement}>
                <Ionicons
                  name={validation.hasLetter ? 'checkmark-circle' : 'ellipse-outline'}
                  size={16}
                  color={validation.hasLetter ? '#2ECC71' : '#8E9297'}
                />
                <Text style={[
                  styles.requirementText,
                  validation.hasLetter && styles.requirementMet
                ]}>                  {t('auth.atLeastOneLetter')}
                </Text>
              </View>

              <View style={styles.requirement}>
                <Ionicons
                  name={validation.hasNumber ? 'checkmark-circle' : 'ellipse-outline'}
                  size={16}
                  color={validation.hasNumber ? '#2ECC71' : '#8E9297'}
                />
                <Text style={[
                  styles.requirementText,
                  validation.hasNumber && styles.requirementMet
                ]}>                  {t('auth.atLeastOneNumber')}
                </Text>
              </View>
            </View>

            {/* Accept Button */}
            <TouchableOpacity
              style={[
                styles.acceptButton,
                (!validation.isValid || password !== confirmPassword || accepting) && styles.acceptButtonDisabled
              ]}
              onPress={handleAcceptInvitation}
              disabled={!validation.isValid || password !== confirmPassword || accepting}
            >
              {accepting ? (
                <ActivityIndicator color="#FFFFFF" />
              ) : (
                <Text style={styles.acceptButtonText}>{t('invitation.createAccount')}</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  safeArea: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#8E9297',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#E74C3C',
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: '#8E9297',
    textAlign: 'center',
    marginBottom: 30,
  },
  backButton: {
    backgroundColor: '#0D3B66',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 30,
  },
  headerBackButton: {
    marginRight: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  invitationInfo: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 12,
    marginBottom: 30,
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  nameText: {
    fontSize: 20,
    fontWeight: '700',
    color: '#0D3B66',
    marginBottom: 4,
  },
  emailText: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 8,
  },
  roleText: {
    fontSize: 12,
    color: '#0D3B66',
    fontWeight: '500',
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  form: {
    flex: 1,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  formDescription: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E1E8ED',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  eyeButton: {
    padding: 12,
  },
  requirementsContainer: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
  },
  requirementsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 12,
  },
  requirement: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  requirementText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#8E9297',
  },
  requirementMet: {
    color: '#2ECC71',
  },
  acceptButton: {
    backgroundColor: '#0D3B66',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  acceptButtonDisabled: {
    opacity: 0.6,
  },
  acceptButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default InvitationScreen;
