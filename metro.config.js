const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Add polyfills for Node.js modules
config.resolver.alias = {
  ...config.resolver.alias,
  stream: path.resolve(__dirname, 'polyfills/stream.js'),
  crypto: 'react-native-crypto',
  vm: 'vm-browserify',
  // Disable Node.js modules not available in React Native
  ws: path.resolve(__dirname, 'polyfills/empty.js'),
  net: path.resolve(__dirname, 'polyfills/empty.js'),
  tls: path.resolve(__dirname, 'polyfills/empty.js'),
  fs: path.resolve(__dirname, 'polyfills/empty.js'),
  child_process: path.resolve(__dirname, 'polyfills/empty.js'),
  http: path.resolve(__dirname, 'polyfills/empty.js'),
  https: path.resolve(__dirname, 'polyfills/empty.js'),
  url: 'react-native-url-polyfill',
  util: path.resolve(__dirname, 'polyfills/empty.js'),
  events: path.resolve(__dirname, 'polyfills/events.js'),
};

// Add polyfill modules to resolver
config.resolver.platforms = ['native', 'web', 'ios', 'android'];

// Exclude problematic Node.js modules completely
// Handle existing blockList properly
const existingBlockList = config.resolver.blockList;
if (existingBlockList && Array.isArray(existingBlockList)) {
  config.resolver.blockList = [
    ...existingBlockList,
    /node_modules[/\\]ws[/\\]/,
  ];
} else {
  config.resolver.blockList = [
    /node_modules[/\\]ws[/\\]/,
  ];
}

// Configure resolver to handle React Native environment
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Ensure React Native components are resolved correctly
config.resolver.sourceExts = [...config.resolver.sourceExts, 'jsx', 'js', 'ts', 'tsx'];

// Add node modules resolution for React Native components
config.resolver.nodeModulesPaths = [
  path.resolve(__dirname, 'node_modules'),
];

// Ensure proper resolution of React Native modules
config.resolver.disableHierarchicalLookup = false;
config.resolver.unstable_enablePackageExports = false;

// Reset transformer to avoid conflicts with React Native components
config.transformer = {
  ...config.transformer,
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: false,
    },
  }),
  minifierConfig: {
    mangle: {
      keep_fnames: true,
    },
    output: {
      ascii_only: true,
      quote_keys: true,
      wrap_iife: true,
    },
    sourceMap: {
      includeSources: false,
    },
    toplevel: false,
    compress: {
      reduce_funcs: false,
    },
  },
};

module.exports = config;
