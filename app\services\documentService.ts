import { supabase } from '../lib/supabase';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import { decode } from 'base64-arraybuffer';

// Document types based on DATABASE-SYSTEM.md
export type DocumentType =
  | 'DEATH_CERT'
  | 'FORMUL_C'
  | 'CARGO_WAYBILL'
  | 'PHOTO';

export interface Document {
  id: string;
  case_id: string;
  doc_type: DocumentType;
  storage_path: string;
  uploaded_by: string;
  created_at: string;
}

export interface DocumentUploadResult {
  success: boolean;
  document?: Document;
  error?: string;
}

export interface DocumentListResult {
  success: boolean;
  documents?: Document[];
  error?: string;
}

class DocumentService {
  // Belge türlerinin Türkçe karşılıkları
  getDocumentTypeLabel(docType: DocumentType): string {
    const labels: Record<DocumentType, string> = {
      'DEATH_CERT': 'Ölüm Belgesi',
      'FORMUL_C': 'Formül C',
      'CARGO_WAYBILL': 'Kargo Belgesi',
      'PHOTO': 'Fotoğraf'
    };
    return labels[docType];
  }

  // Belge seçme seçenekleri
  async showDocumentPicker(): Promise<{ type: 'camera' | 'gallery' | 'file' | null }> {
    return new Promise((resolve) => {
      // Bu fonksiyon Alert.alert ile seçenek sunacak
      // Şimdilik basit bir implementasyon
      resolve({ type: 'file' });
    });
  }

  // Kameradan fotoğraf çek
  async pickFromCamera(): Promise<{ success: boolean; result?: DocumentPicker.DocumentPickerResult; error?: string }> {
    try {
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();

      if (!permissionResult.granted) {
        return {
          success: false,
          error: 'Kamera izni gerekli. Lütfen ayarlardan kamera iznini açın.'
        };
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images, // Geri eski versiyona dön
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        return {
          success: true,
          result: {
            canceled: false,
            assets: [{
              uri: result.assets[0].uri,
              name: `photo_${Date.now()}.jpg`,
              mimeType: 'image/jpeg',
              size: result.assets[0].fileSize || 0,
            }]
          } as DocumentPicker.DocumentPickerResult
        };
      }

      return {
        success: false,
        error: 'Fotoğraf çekme iptal edildi'
      };
    } catch (error) {
      console.error('DocumentService: Camera error:', error);
      return {
        success: false,
        error: 'Kamera açılırken hata oluştu'
      };
    }
  }

  // Galeriden fotoğraf seç
  async pickFromGallery(): Promise<{ success: boolean; result?: DocumentPicker.DocumentPickerResult; error?: string }> {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        return {
          success: false,
          error: 'Galeri izni gerekli. Lütfen ayarlardan galeri iznini açın.'
        };
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images, // Geri eski versiyona dön
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        return {
          success: true,
          result: {
            canceled: false,
            assets: [{
              uri: result.assets[0].uri,
              name: `image_${Date.now()}.jpg`,
              mimeType: 'image/jpeg',
              size: result.assets[0].fileSize || 0,
            }]
          } as DocumentPicker.DocumentPickerResult
        };
      }

      return {
        success: false,
        error: 'Fotoğraf seçme iptal edildi'
      };
    } catch (error) {
      console.error('DocumentService: Gallery error:', error);
      return {
        success: false,
        error: 'Galeri açılırken hata oluştu'
      };
    }
  }

  // Dosya seç
  async pickDocument(): Promise<{ success: boolean; result?: DocumentPicker.DocumentPickerResult; error?: string }> {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['image/*', 'application/pdf', 'text/*'],
        copyToCacheDirectory: true,
      });

      if (!result.canceled) {
        return {
          success: true,
          result: result
        };
      }

      return {
        success: false,
        error: 'Dosya seçme iptal edildi'
      };
    } catch (error) {
      console.error('DocumentService: Document picker error:', error);
      return {
        success: false,
        error: 'Dosya seçerken hata oluştu'
      };
    }
  }

  // Dosyayı Supabase Storage'a yükle
  async uploadToStorage(file: any, caseId: string, docType: DocumentType): Promise<string | null> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${caseId}/${docType}_${Date.now()}.${fileExt}`;

      // Dosyayı base64'e çevir
      const response = await fetch(file.uri);
      const blob = await response.blob();
      const arrayBuffer = await blob.arrayBuffer();

      const { data, error } = await supabase.storage
        .from('documents')
        .upload(fileName, arrayBuffer, {
          contentType: file.mimeType || 'application/octet-stream',
          upsert: false
        });

      if (error) {
        console.error('DocumentService: Storage upload error:', error);
        return null;
      }

      return data.path;
    } catch (error) {
      console.error('DocumentService: Upload error:', error);
      return null;
    }
  }

  // Belgeyi veritabanına kaydet
  async saveDocument(
    caseId: string,
    docType: DocumentType,
    storagePath: string,
    uploadedBy: string
  ): Promise<DocumentUploadResult> {
    try {
      const { data, error } = await supabase
        .from('documents')
        .insert({
          case_id: caseId,
          doc_type: docType,
          storage_path: storagePath,
          uploaded_by: uploadedBy,
        })
        .select()
        .single();

      if (error) {
        console.error('DocumentService: Database save error:', error);
        return {
          success: false,
          error: 'Belge veritabanına kaydedilemedi',
        };
      }

      return {
        success: true,
        document: data,
      };
    } catch (error) {
      console.error('DocumentService: Save document error:', error);
      return {
        success: false,
        error: 'Beklenmeyen hata oluştu',
      };
    }
  }

  // Vaka belgelerini listele
  async getDocumentsByCase(caseId: string): Promise<DocumentListResult> {
    try {
      const { data, error } = await supabase
        .from('documents')
        .select(`
          *,
          uploader:uploaded_by(full_name)
        `)
        .eq('case_id', caseId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('DocumentService: Get documents error:', error);
        return {
          success: false,
          error: 'Belgeler yüklenemedi',
        };
      }

      return {
        success: true,
        documents: data || [],
      };
    } catch (error) {
      console.error('DocumentService: Get documents unexpected error:', error);
      return {
        success: false,
        error: 'Beklenmeyen hata oluştu',
      };
    }
  }

  // Belgeyi sil
  async deleteDocument(documentId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('documents')
        .delete()
        .eq('id', documentId);

      if (error) {
        console.error('DocumentService: Delete document error:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('DocumentService: Delete document unexpected error:', error);
      return false;
    }
  }

  // Belge URL'si al
  async getDocumentUrl(storagePath: string): Promise<string | null> {
    try {
      const { data } = await supabase.storage
        .from('documents')
        .createSignedUrl(storagePath, 3600); // 1 saat geçerli

      return data?.signedUrl || null;
    } catch (error) {
      console.error('DocumentService: Get URL error:', error);
      return null;
    }
  }
}

export const documentService = new DocumentService();
