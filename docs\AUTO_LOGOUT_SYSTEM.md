# Otomatik Logout Sistemi

Bu dokümantasyon, uygulamaya eklenen otomatik logout sisteminin nasıl çalıştığını açıklar.

## Özellikler

### 1. **Session Timeout**
- Kullanıcı belirli bir süre boyunca uygulamayı kullanmazsa otomatik olarak çıkış yapar
- Varsayılan timeout süresi: **10 dakika**
- Timeout süresinden **2 dakika önce** kullanıcıya uyarı gösterilir

### 2. **User Activity Tracking**
- Dokunma (touch) olayları
- Scroll olayları
- Sayfa geçişleri (navigation)
- Herhang<PERSON> bir kullanıcı etkileşimi aktivite olarak kaydedilir

### 3. **Background/Foreground Handling**
- Uygulama background'a geçtiğinde timer durur
- Foreground'a döndüğünde kaldığı yerden devam eder
- Background'da geçen süre timeout süresine eklenir

### 4. **Warning Modal**
- Timeout'tan 2 dakika önce uyarı modal'ı gösterilir
- Kullanıcı "Oturumu Uzat" seçeneği ile session'ı uzatabilir
- Kullanıcı "Çıkış Yap" seçeneği ile manuel çıkış yapabilir
- Geri sayım timer'ı ile kalan süre gösterilir

## Teknik Detaylar

### Dosya Yapısı

```
app/
├── constants/config.ts              # Timeout konfigürasyonu
├── services/sessionTimeoutService.ts # Ana timeout servisi
├── store/authStore.ts               # Auth state management
├── hooks/useUserActivity.ts         # User activity tracking hook
├── components/SessionTimeoutModal.tsx # Warning modal component
├── utils/navigationActivityTracker.ts # Navigation event tracking
└── navigation/
    ├── RootNavigator.tsx            # Ana navigator (modal integration)
    └── RoleBasedNavigator.tsx       # Role-based navigator (activity tracking)
```

### Konfigürasyon

`app/constants/config.ts` dosyasında timeout ayarları:

```typescript
session: {
  timeoutMinutes: 10,        // 10 dakika sonra otomatik çıkış
  warningMinutes: 2,         // 2 dakika kala uyarı göster
  checkIntervalSeconds: 60,  // Her 60 saniyede bir kontrol et
}
```

### Ana Servis: SessionTimeoutService

**Özellikler:**
- Timer management (timeout, warning, periodic check)
- App state monitoring (background/foreground)
- Activity tracking
- AsyncStorage ile session persistence

**Önemli Metodlar:**
- `start()` - Session timeout'u başlatır
- `stop()` - Session timeout'u durdurur
- `recordActivity()` - Kullanıcı aktivitesini kaydeder
- `extendSession()` - Oturumu uzatır

### Auth Store Integration

`useAuthStore` hook'u ile entegrasyon:

```typescript
// Session timeout fonksiyonları
startSessionTimeout()    // Timeout'u başlat
stopSessionTimeout()     // Timeout'u durdur
recordUserActivity()     // Aktivite kaydet
extendSession()          // Oturumu uzat
handleSessionWarning()   // Warning modal'ı göster
handleSessionTimeout()   // Otomatik çıkış yap
```

### User Activity Tracking

**useUserActivity Hook:**
- PanResponder ile touch event'leri yakalar
- Throttling ile çok fazla çağrı önlenir (5 saniye aralık)
- Navigation event'leri otomatik olarak kaydedilir

**Kullanım:**
```typescript
const { panResponder } = useUserActivity();

<View {...panResponder.panHandlers}>
  {/* App content */}
</View>
```

## Test Etme

### Test Screen'i
Admin panelinde "Session Test" butonu ile test ekranına erişilebilir.

**Test Özellikleri:**
- Gerçek zamanlı session bilgileri
- Manuel aktivite kaydetme
- Oturum uzatma
- Zorla timeout tetikleme
- Timer başlatma/durdurma

### Test İçin Hızlı Konfigürasyon

Test etmek için `config.ts`'de süreleri kısaltabilirsiniz:

```typescript
session: {
  timeoutMinutes: 2,         // 2 dakika
  warningMinutes: 0.5,       // 30 saniye
  checkIntervalSeconds: 10,  // 10 saniye
}
```

## Kullanım Senaryoları

### 1. Normal Kullanım
- Kullanıcı uygulamayı aktif olarak kullanır
- Her etkileşimde timer reset edilir
- 10 dakika boyunca aktivite yoksa uyarı gösterilir

### 2. Background/Foreground
- Uygulama background'a geçer → Timer durur
- Foreground'a döner → Timer kaldığı yerden devam eder
- Background'da geçen süre + önceki süre > 10 dakika ise otomatik çıkış

### 3. Warning Modal
- 8 dakika sonra warning modal'ı gösterilir
- Kullanıcı "Oturumu Uzat" seçerse → Timer reset edilir
- Kullanıcı hiçbir şey yapmazsa → 2 dakika sonra otomatik çıkış

### 4. Otomatik Çıkış
- Session timeout olduğunda otomatik olarak logout yapılır
- Kullanıcı login ekranına yönlendirilir
- Tüm session verileri temizlenir

## Güvenlik Özellikleri

- **Client-side timeout:** Güvenlik için ek bir katman
- **Session persistence:** App restart'ta session korunur
- **Background handling:** Güvenlik açığı önlenir
- **Activity validation:** Sadece gerçek kullanıcı etkileşimleri sayılır

## Performans Optimizasyonları

- **Throttling:** Activity tracking'de çok fazla çağrı önlenir
- **Efficient timers:** Sadece gerekli timer'lar çalışır
- **Memory management:** Timer'lar düzgün temizlenir
- **Background optimization:** Background'da gereksiz işlem yapılmaz

## Troubleshooting

### Yaygın Sorunlar

1. **Timer çalışmıyor**
   - Auth store'da `startSessionTimeout()` çağrıldığından emin olun
   - User authenticated olduğunu kontrol edin

2. **Activity tracking çalışmıyor**
   - PanResponder'ın doğru şekilde uygulandığından emin olun
   - Navigation listener'ın kurulduğunu kontrol edin

3. **Warning modal görünmüyor**
   - `showSessionWarning` state'inin doğru şekilde yönetildiğini kontrol edin
   - Modal component'inin render edildiğinden emin olun

### Debug Logları

Console'da şu logları görebilirsiniz:
- `SessionTimeoutService: Starting session timeout monitoring`
- `SessionTimeoutService: User activity recorded`
- `SessionTimeoutService: Warning timer triggered`
- `SessionTimeoutService: Timeout timer triggered`

## Gelecek Geliştirmeler

- [ ] Server-side session validation
- [ ] Configurable timeout per user role
- [ ] Activity type-based timeout (different timeouts for different actions)
- [ ] Session analytics and reporting
- [ ] Push notification for session expiry warnings
