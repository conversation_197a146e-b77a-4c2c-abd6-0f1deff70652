# Funeral App Database System

Bu klasör, DATABASE-SYSTEM.md spesifikasyonlarına göre oluşturulmuş veritabanı dosyalarını içerir.

## Dosya Yapısı

### 1. `01-schema.sql`
- <PERSON><PERSON><PERSON> ENUM tipleri
- Temel tablolar (users, deceased, cases, documents, notifications, lookup_values)
- <PERSON><PERSON> ta<PERSON> (places, tasks, driver_locations, task_events)
- İndeksler

### 2. `02-rls-policies.sql`
- Row Level Security (RLS) politikaları
- Rol bazlı erişim kontrolü
- ADMIN, DRIVER, FAMILY rolleri için ayrı politikalar

### 3. `03-functions.sql`
- <PERSON>gger fonksiyonları
- Yardımcı fonksiyonlar
- <PERSON>fe hesaplama (Haversine formülü)
- Görev atama ve tamamlama fonksiyonları

### 4. `04-test-data.sql`
- Test verileri
- <PERSON><PERSON><PERSON> (Ad<PERSON>, <PERSON>, <PERSON><PERSON>)
- <PERSON>rne<PERSON> vakalar ve görevler
- Örnek konumlar ve belgeler

## Kurulum Sırası

Supabase SQL Editor'de şu sırayla çalıştırın:

```sql
-- 1. Şema ve tablolar
-- 01-schema.sql dosyasının içeriğini kopyalayıp çalıştırın

-- 2. RLS politikaları (BASİTLEŞTİRİLMİŞ)
-- 02-rls-policies-simple.sql dosyasının içeriğini kopyalayıp çalıştırın
-- NOT: 02-rls-policies.sql yerine 02-rls-policies-simple.sql kullanın!

-- 3. Fonksiyonlar ve trigger'lar
-- 03-functions.sql dosyasının içeriğini kopyalayıp çalıştırın

-- 4. Test verileri (opsiyonel ama önerilen)
-- 04-test-data.sql dosyasının içeriğini kopyalayıp çalıştırın
```

## ⚠️ ÖNEMLİ: RLS Circular Reference Sorunu

Eğer `"infinite recursion detected in policy for relation \"users\""` hatası alıyorsanız:

1. **Basitleştirilmiş RLS kullanın:**
   ```sql
   -- 02-rls-policies-simple.sql dosyasını çalıştırın
   ```

2. **Veya RLS'yi tamamen devre dışı bırakın:**
   ```sql
   ALTER TABLE users DISABLE ROW LEVEL SECURITY;
   ALTER TABLE deceased DISABLE ROW LEVEL SECURITY;
   ALTER TABLE cases DISABLE ROW LEVEL SECURITY;
   -- ... diğer tablolar için de
   ```

## Debug ve Sorun Giderme

### Kullanıcı Giriş Sorunları

Eğer `<EMAIL>` veya diğer test kullanıcıları ile giriş yapamıyorsanız:

1. **Veritabanı durumunu kontrol edin:**
   ```sql
   -- debug-users.sql dosyasını Supabase SQL Editor'de çalıştırın
   ```

2. **Test verilerinin yüklenip yüklenmediğini kontrol edin:**
   ```sql
   SELECT COUNT(*) FROM users WHERE email = '<EMAIL>';
   ```

3. **Eğer kullanıcı bulunamıyorsa, test verilerini yeniden yükleyin:**
   ```sql
   -- 04-test-data.sql dosyasını tekrar çalıştırın
   ```

## Önemli Notlar

### Kullanıcı Rolleri
- **ADMIN**: Tüm verilere erişim, görev atama
- **DRIVER**: Atanan görevleri görme ve güncelleme
- **FAMILY**: Kendi vakalarını görme

### Görev Akışı
1. **ASSIGNED**: Görev atandı, sürücü başlatmadı
2. **IN_PROGRESS**: Sürücü görevi başlattı
3. **COMPLETED**: Görev tamamlandı
4. **FAILED**: Görev başarısız/iptal

### Canlı Takip
- `driver_locations`: Sürücü konumları (15-30 saniye aralıklarla)
- `task_events`: Tüm görev olayları (audit log)
- Otomatik mesafe ve ETA hesaplama

### Güvenlik
- RLS tüm tablolarda aktif
- Rol bazlı veri erişimi
- Trigger'lar ile otomatik log tutma

## API Entegrasyonu

### Temel Sorgular

```sql
-- Aktif sürücüler
SELECT * FROM users WHERE role = 'DRIVER' AND status = 'ACTIVE';

-- Açık vakalar
SELECT * FROM cases WHERE status = 'OPEN';

-- Sürücünün görevleri
SELECT * FROM tasks WHERE assignee_id = $1 AND status IN ('ASSIGNED', 'IN_PROGRESS');

-- Vaka istatistikleri
SELECT * FROM get_case_statistics();
```

### Görev İşlemleri

```sql
-- Görev atama
SELECT assign_task_to_driver($task_id, $driver_id, $scheduled_at);

-- Görev tamamlama
SELECT complete_task($task_id, $driver_id, $lat, $lng);

-- Bildirim gönderme
SELECT send_notification($case_id, $channel, $recipient, $template);
```

## Performans Optimizasyonu

### İndeksler
- Rol ve durum bazlı kullanıcı sorguları
- Vaka ve görev ilişkileri
- Zaman bazlı sorgular için DESC indeksler

### Partitioning (Önerilen)
```sql
-- driver_locations tablosu için aylık partisyon
CREATE TABLE driver_locations_2025_01 
PARTITION OF driver_locations
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

### Temizlik
```sql
-- Eski konum verilerini temizle (30 gün)
SELECT cleanup_old_driver_locations(30);
```

## Veri Modeli

### Temel İlişkiler
- `users` ← `cases` (family_user_id)
- `deceased` ← `cases` (deceased_id)
- `cases` ← `tasks` (case_id)
- `users` ← `tasks` (assignee_id)
- `places` ← `tasks` (origin_place_id, dest_place_id)

### Audit Trail
- `task_events`: Tüm görev değişiklikleri
- `driver_locations`: Konum geçmişi
- `notifications`: Bildirim geçmişi

## Troubleshooting

### RLS Sorunları
```sql
-- RLS'yi geçici olarak devre dışı bırak (sadece test için)
ALTER TABLE table_name DISABLE ROW LEVEL SECURITY;
```

### Trigger Sorunları
```sql
-- Trigger'ları kontrol et
SELECT * FROM information_schema.triggers WHERE trigger_schema = 'public';
```

### Performans Sorunları
```sql
-- Yavaş sorguları analiz et
EXPLAIN ANALYZE SELECT ...;
```

## Geliştirme Notları

- Tüm UUID'ler otomatik oluşturulur
- Timestamp'ler UTC olarak saklanır
- ENUM değerleri değiştirilmeden önce mevcut veriler kontrol edilmeli
- Yeni roller eklendiğinde RLS politikaları güncellenmeli
