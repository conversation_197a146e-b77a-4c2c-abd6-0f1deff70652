import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Platform,
  StatusBar,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User } from '../services/authService';
import { useAlertHelpers } from '../hooks/useAlert';
import { useTranslation } from 'react-i18next';
import { supabase } from '../lib/supabase';

interface ProfileScreenProps {
  user: User;
  onBack: () => void;
  onLogout: () => void;
}

const ProfileScreen = ({ user, onBack, onLogout }: ProfileScreenProps) => {
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(user.full_name || '');
  const [editedEmail, setEditedEmail] = useState(user.email);

  const { showError, showSuccess, showInfo, showConfirm } = useAlertHelpers();

  const roleColors = {
    ADMIN: '#E74C3C',
    DRIVER: '#2ECC71',
    FAMILY: '#9B59B6',
  };

  const roleLabels = {
    ADMIN: t('roles.admin'),
    DRIVER: t('roles.driver'),
    FAMILY: t('roles.family'),
  };

  const handleSave = () => {
    if (!editedName.trim()) {
      showError(t('common.error'), t('profile.nameRequired'));
      return;
    }

    // TODO: API call to update profile
    showSuccess(t('common.success'), t('profile.profileUpdated'), () => setIsEditing(false));
  };

  const handleCancel = () => {
    setEditedName(user.full_name || '');
    setEditedEmail(user.email);
    setIsEditing(false);
  };

  const [showChangePassword, setShowChangePassword] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmNewPassword, setShowConfirmNewPassword] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);

  const validatePassword = (pwd: string) => {
    const minLength = pwd.length >= 8;
    const hasNumber = /\d/.test(pwd);
    const hasLetter = /[a-zA-Z]/.test(pwd);

    return {
      minLength,
      hasNumber,
      hasLetter,
      isValid: minLength && hasNumber && hasLetter
    };
  };

  const handleChangePassword = () => {
    setShowChangePassword(true);
  };

  const handlePasswordChange = async () => {
    if (!currentPassword.trim() || !newPassword.trim() || !confirmNewPassword.trim()) {
      showError(t('common.error'), t('alerts.fillAllFields'));
      return;
    }

    if (newPassword !== confirmNewPassword) {
      showError(t('common.error'), t('auth.passwordMismatch'));
      return;
    }

    const validation = validatePassword(newPassword);
    if (!validation.isValid) {
      showError(t('auth.invalidPassword'), t('auth.passwordRequirements'));
      return;
    }

    setPasswordLoading(true);
    try {
      // First verify current password by attempting to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user.email,
        password: currentPassword,
      });

      if (signInError) {
        showError(t('common.error'), t('profile.currentPasswordIncorrect'));
        return;
      }

      // Update password
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (updateError) {
        showError(t('common.error'), t('profile.passwordUpdateFailed'));
        return;
      }

      showSuccess(t('common.success'), t('profile.passwordUpdatedSuccessfully'), () => {
        setShowChangePassword(false);
        setCurrentPassword('');
        setNewPassword('');
        setConfirmNewPassword('');
      });
    } catch (error) {
      showError(t('common.error'), t('profile.passwordUpdateFailed'));
    } finally {
      setPasswordLoading(false);
    }
  };

  const handleCancelPasswordChange = () => {
    setShowChangePassword(false);
    setCurrentPassword('');
    setNewPassword('');
    setConfirmNewPassword('');
  };

  const handleLogout = () => {
    showConfirm(
      t('profile.logout'),
      t('profile.logoutConfirm'),
      onLogout,
      undefined,
      t('profile.logout'),
      t('common.cancel')
    );
  };

  const profileSections = [
    {
      title: t('profile.accountInfo'),
      items: [
        {
          icon: 'person',
          label: t('profile.fullName'),
          value: isEditing ? editedName : (user.full_name || t('profile.notSpecified')),
          editable: true,
        },
        {
          icon: 'mail',
          label: t('profile.email'),
          value: user.email,
          editable: false,
        },
        {
          icon: 'shield',
          label: t('profile.role'),
          value: roleLabels[user.role as keyof typeof roleLabels] || user.role,
          editable: false,
        },
      ],
    },
    {
      title: t('profile.security'),
      items: [
        {
          icon: 'key',
          label: t('auth.password'),
          value: '••••••••',
          editable: false,
          action: handleChangePassword,
        },
      ],
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#0D3B66" />
        </TouchableOpacity>
        <Text style={styles.title}>{t('profile.title')}</Text>
        {!isEditing ? (
          <TouchableOpacity onPress={() => setIsEditing(true)} style={styles.editButton}>
            <Ionicons name="create" size={24} color="#0D3B66" />
          </TouchableOpacity>
        ) : (
          <View style={styles.editActions}>
            <TouchableOpacity onPress={handleCancel} style={styles.cancelButton}>
              <Ionicons name="close" size={20} color="#E74C3C" />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleSave} style={styles.saveButton}>
              <Ionicons name="checkmark" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        )}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Avatar */}
        <View style={styles.avatarSection}>
          <View style={[
            styles.avatar,
            { backgroundColor: roleColors[user.role as keyof typeof roleColors] || '#0D3B66' }
          ]}>
            <Text style={styles.avatarText}>
              {(user.full_name || user.email)
                .split(' ')
                .map(n => n[0])
                .join('')
                .toUpperCase()
                .slice(0, 2)}
            </Text>
          </View>
          <Text style={styles.userName}>{user.full_name || t('profile.user')}</Text>
          <View style={[
            styles.roleBadge,
            { backgroundColor: roleColors[user.role as keyof typeof roleColors] || '#0D3B66' }
          ]}>
            <Text style={styles.roleText}>
              {roleLabels[user.role as keyof typeof roleLabels] || user.role}
            </Text>
          </View>
        </View>

        {/* Profile Sections */}
        {profileSections.map((section, sectionIndex) => (
          <View key={sectionIndex} style={styles.section}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            {section.items.map((item, itemIndex) => (
              <View key={itemIndex} style={styles.profileItem}>
                <View style={styles.itemLeft}>
                  <View style={styles.itemIcon}>
                    <Ionicons name={item.icon as any} size={16} color="#0D3B66" />
                  </View>
                  <View style={styles.itemContent}>
                    <Text style={styles.itemLabel}>{item.label}</Text>
                    {isEditing && item.editable && item.label === t('profile.fullName') ? (
                      <TextInput
                        style={styles.editInput}
                        value={editedName}
                        onChangeText={setEditedName}
                        placeholder={t('profile.fullName')}
                        autoFocus
                      />
                    ) : (
                      <Text style={styles.itemValue}>{item.value}</Text>
                    )}
                  </View>
                </View>
                {item.action && !isEditing && (
                  <TouchableOpacity onPress={item.action} style={styles.actionButton}>
                    <Text style={styles.actionButtonText}>{t('common.change')}</Text>
                  </TouchableOpacity>
                )}
              </View>
            ))}
          </View>
        ))}

        {/* App Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('profile.application')}</Text>
          <View style={styles.profileItem}>
            <View style={styles.itemLeft}>
              <View style={styles.itemIcon}>
                <Ionicons name="information-circle" size={16} color="#0D3B66" />
              </View>
              <View style={styles.itemContent}>
                <Text style={styles.itemLabel}>{t('profile.version')}</Text>
                <Text style={styles.itemValue}>v1.0.0</Text>
              </View>
            </View>
          </View>
          <View style={styles.profileItem}>
            <View style={styles.itemLeft}>
              <View style={styles.itemIcon}>
                <Ionicons name="time" size={16} color="#0D3B66" />
              </View>
              <View style={styles.itemContent}>
                <Text style={styles.itemLabel}>{t('profile.lastLogin')}</Text>
                <Text style={styles.itemValue}>
                  {new Date().toLocaleDateString('tr-TR', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Logout Button */}
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Ionicons name="log-out" size={16} color="#E74C3C" />
          <Text style={styles.logoutButtonText}>{t('profile.logout')}</Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Change Password Modal */}
      <Modal
        visible={showChangePassword}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={handleCancelPasswordChange}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={handleCancelPasswordChange} style={styles.modalBackButton}>
              <Ionicons name="arrow-back" size={24} color="#0D3B66" />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>{t('profile.changePassword')}</Text>
            <View style={styles.modalHeaderSpacer} />
          </View>

          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            <Text style={styles.modalDescription}>
              {t('profile.changePasswordDescription')}
            </Text>

            {/* Current Password */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>{t('profile.currentPassword')}</Text>
              <View style={styles.passwordInputContainer}>
                <TextInput
                  style={styles.passwordInput}
                  value={currentPassword}
                  onChangeText={setCurrentPassword}
                  placeholder={t('profile.enterCurrentPassword')}
                  secureTextEntry={!showCurrentPassword}
                  autoCapitalize="none"
                />
                <TouchableOpacity
                  onPress={() => setShowCurrentPassword(!showCurrentPassword)}
                  style={styles.eyeButton}
                >
                  <Ionicons
                    name={showCurrentPassword ? 'eye-off' : 'eye'}
                    size={20}
                    color="#666"
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* New Password */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>{t('auth.newPassword')}</Text>
              <View style={styles.passwordInputContainer}>
                <TextInput
                  style={styles.passwordInput}
                  value={newPassword}
                  onChangeText={setNewPassword}
                  placeholder={t('profile.enterNewPassword')}
                  secureTextEntry={!showNewPassword}
                  autoCapitalize="none"
                />
                <TouchableOpacity
                  onPress={() => setShowNewPassword(!showNewPassword)}
                  style={styles.eyeButton}
                >
                  <Ionicons
                    name={showNewPassword ? 'eye-off' : 'eye'}
                    size={20}
                    color="#666"
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Confirm New Password */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>{t('auth.confirmPassword')}</Text>
              <View style={styles.passwordInputContainer}>
                <TextInput
                  style={styles.passwordInput}
                  value={confirmNewPassword}
                  onChangeText={setConfirmNewPassword}
                  placeholder={t('profile.confirmNewPassword')}
                  secureTextEntry={!showConfirmNewPassword}
                  autoCapitalize="none"
                />
                <TouchableOpacity
                  onPress={() => setShowConfirmNewPassword(!showConfirmNewPassword)}
                  style={styles.eyeButton}
                >
                  <Ionicons
                    name={showConfirmNewPassword ? 'eye-off' : 'eye'}
                    size={20}
                    color="#666"
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Password Requirements */}
            <View style={styles.requirementsContainer}>
              <Text style={styles.requirementsTitle}>{t('auth.passwordRequirementsTitle')}</Text>
              <View style={styles.requirement}>
                <Ionicons
                  name={newPassword.length >= 8 ? 'checkmark-circle' : 'ellipse-outline'}
                  size={16}
                  color={newPassword.length >= 8 ? '#2ECC71' : '#BDC3C7'}
                />
                <Text style={[styles.requirementText, { color: newPassword.length >= 8 ? '#2ECC71' : '#7F8C8D' }]}>
                  {t('auth.minSixChars')}
                </Text>
              </View>
              <View style={styles.requirement}>
                <Ionicons
                  name={/[a-zA-Z]/.test(newPassword) ? 'checkmark-circle' : 'ellipse-outline'}
                  size={16}
                  color={/[a-zA-Z]/.test(newPassword) ? '#2ECC71' : '#BDC3C7'}
                />
                <Text style={[styles.requirementText, { color: /[a-zA-Z]/.test(newPassword) ? '#2ECC71' : '#7F8C8D' }]}>
                  {t('auth.atLeastOneLetter')}
                </Text>
              </View>
              <View style={styles.requirement}>
                <Ionicons
                  name={/\d/.test(newPassword) ? 'checkmark-circle' : 'ellipse-outline'}
                  size={16}
                  color={/\d/.test(newPassword) ? '#2ECC71' : '#BDC3C7'}
                />
                <Text style={[styles.requirementText, { color: /\d/.test(newPassword) ? '#2ECC71' : '#7F8C8D' }]}>
                  {t('auth.atLeastOneNumber')}
                </Text>
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.cancelModalButton}
                onPress={handleCancelPasswordChange}
              >
                <Text style={styles.cancelModalButtonText}>{t('common.cancel')}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.savePasswordButton, { opacity: passwordLoading ? 0.7 : 1 }]}
                onPress={handlePasswordChange}
                disabled={passwordLoading}
              >
                {passwordLoading ? (
                  <ActivityIndicator color="#FFFFFF" size="small" />
                ) : (
                  <Text style={styles.savePasswordButtonText}>{t('profile.updatePassword')}</Text>
                )}
              </TouchableOpacity>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  editActions: {
    flexDirection: 'row',
    gap: 8,
  },
  cancelButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#2ECC71',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  avatarSection: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatarText: {
    fontSize: 22,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  userName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 6,
  },
  roleBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  roleText: {
    fontSize: 11,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 12,
  },
  profileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 10,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  itemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  itemContent: {
    flex: 1,
  },
  itemLabel: {
    fontSize: 12,
    color: '#8E9297',
    marginBottom: 3,
  },
  itemValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
  },
  editInput: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
    borderBottomWidth: 1,
    borderBottomColor: '#0D3B66',
    paddingVertical: 3,
  },
  actionButton: {
    backgroundColor: '#0D3B66',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  actionButtonText: {
    fontSize: 11,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 10,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#E74C3C',
  },
  logoutButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#E74C3C',
    marginLeft: 6,
  },
  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  modalBackButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  modalHeaderSpacer: {
    width: 40,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 24,
  },
  modalDescription: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 24,
    lineHeight: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  passwordInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E1E8ED',
    paddingHorizontal: 16,
  },
  passwordInput: {
    flex: 1,
    fontSize: 14,
    color: '#1C1C1E',
    paddingVertical: 16,
  },
  eyeButton: {
    padding: 4,
  },
  requirementsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  requirementsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 12,
  },
  requirement: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  requirementText: {
    fontSize: 12,
    marginLeft: 8,
    flex: 1,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    paddingBottom: 24,
  },
  cancelModalButton: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    borderRadius: 10,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  cancelModalButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#8E9297',
  },
  savePasswordButton: {
    flex: 1,
    backgroundColor: '#0D3B66',
    paddingVertical: 16,
    borderRadius: 10,
    alignItems: 'center',
  },
  savePasswordButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

export default ProfileScreen;
