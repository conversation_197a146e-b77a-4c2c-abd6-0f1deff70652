{"logs": [{"outputFile": "com.ditib.funeralapp-mergeDebugResources-36:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\284bdced76504d9ec0db1a02932d1b74\\transformed\\material-1.9.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,374,452,530,618,726,817,913,1029,1112,1179,1270,1336,1399,1487,1549,1616,1674,1745,1804,1858,1972,2032,2095,2149,2222,2341,2427,2510,2649,2734,2821,2909,2966,3017,3083,3155,3231,3321,3394,3471,3552,3626,3716,3795,3886,3982,4056,4137,4232,4286,4352,4439,4525,4587,4651,4714,4821,4913,5011,5103,5164,5219", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,77,77,87,107,90,95,115,82,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,82,138,84,86,87,56,50,65,71,75,89,72,76,80,73,89,78,90,95,73,80,94,53,65,86,85,61,63,62,106,91,97,91,60,54,81", "endOffsets": "369,447,525,613,721,812,908,1024,1107,1174,1265,1331,1394,1482,1544,1611,1669,1740,1799,1853,1967,2027,2090,2144,2217,2336,2422,2505,2644,2729,2816,2904,2961,3012,3078,3150,3226,3316,3389,3466,3547,3621,3711,3790,3881,3977,4051,4132,4227,4281,4347,4434,4520,4582,4646,4709,4816,4908,5006,5098,5159,5214,5296"}, "to": {"startLines": "2,36,37,38,39,40,50,51,52,54,55,57,60,62,63,64,65,66,67,68,69,70,71,72,73,74,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3222,3300,3378,3466,3574,4596,4692,4808,4977,5044,5209,5437,5572,5660,5722,5789,5847,5918,5977,6031,6145,6205,6268,6322,6395,6736,6822,6905,7044,7129,7216,7304,7361,7412,7478,7550,7626,7716,7789,7866,7947,8021,8111,8190,8281,8377,8451,8532,8627,8681,8747,8834,8920,8982,9046,9109,9216,9308,9406,9498,9559,9614", "endLines": "7,36,37,38,39,40,50,51,52,54,55,57,60,62,63,64,65,66,67,68,69,70,71,72,73,74,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "endColumns": "12,77,77,87,107,90,95,115,82,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,82,138,84,86,87,56,50,65,71,75,89,72,76,80,73,89,78,90,95,73,80,94,53,65,86,85,61,63,62,106,91,97,91,60,54,81", "endOffsets": "419,3295,3373,3461,3569,3660,4687,4803,4886,5039,5130,5270,5495,5655,5717,5784,5842,5913,5972,6026,6140,6200,6263,6317,6390,6509,6817,6900,7039,7124,7211,7299,7356,7407,7473,7545,7621,7711,7784,7861,7942,8016,8106,8185,8276,8372,8446,8527,8622,8676,8742,8829,8915,8977,9041,9104,9211,9303,9401,9493,9554,9609,9691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\043814979a7351adeafae334529a11be\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,237,333,419,493,567,655,727,794,870,949,1037,1123,1195,1276,1361,1437,1519,1602,1679,1752,1825,1910,1984,2064,2134", "endColumns": "73,107,95,85,73,73,87,71,66,75,78,87,85,71,80,84,75,81,82,76,72,72,84,73,79,69,84", "endOffsets": "124,232,328,414,488,562,650,722,789,865,944,1032,1118,1190,1271,1356,1432,1514,1597,1674,1747,1820,1905,1979,2059,2129,2214"}, "to": {"startLines": "35,48,49,53,56,58,59,61,75,76,77,115,116,117,118,120,121,122,123,124,125,126,127,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3148,4392,4500,4891,5135,5275,5349,5500,6514,6581,6657,9696,9784,9870,9942,10105,10190,10266,10348,10431,10508,10581,10654,10840,10914,10994,11064", "endColumns": "73,107,95,85,73,73,87,71,66,75,78,87,85,71,80,84,75,81,82,76,72,72,84,73,79,69,84", "endOffsets": "3217,4495,4591,4972,5204,5344,5432,5567,6576,6652,6731,9779,9865,9937,10018,10185,10261,10343,10426,10503,10576,10649,10734,10909,10989,11059,11144"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\97dafb02ce1c76f135d92386cd4ea378\\transformed\\core-1.12.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "41,42,43,44,45,46,47,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3665,3765,3867,3968,4069,4174,4279,10739", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "3760,3862,3963,4064,4169,4274,4387,10835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dbbbc453ac84f8e1f55ee9b7498bc2\\transformed\\appcompat-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,2906"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "424,533,635,743,829,934,1052,1133,1212,1303,1396,1491,1585,1685,1778,1873,1968,2059,2150,2249,2355,2461,2559,2666,2773,2878,3048,10023", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "528,630,738,824,929,1047,1128,1207,1298,1391,1486,1580,1680,1773,1868,1963,2054,2145,2244,2350,2456,2554,2661,2768,2873,3043,3143,10100"}}]}]}