import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Platform,
  StatusBar,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User, authService } from '../services/authService';
import { caseService } from '../services/caseService';
import { taskService } from '../services/taskService';
import { userService } from '../services/userService';

// Import screens (will create these)
import AdminDashboardScreen from '../screens/admin/AdminDashboardScreen';
import UsersManagementScreen from '../screens/admin/UsersManagementScreen';
import CasesManagementScreen from '../screens/admin/CasesManagementScreen';
import TasksManagementScreen from '../screens/admin/TasksManagementScreen';
import DocumentsManagementScreen from '../screens/admin/DocumentsManagementScreen';
import NotificationsScreen from '../screens/NotificationsScreen';
import LanguageScreen from '../screens/LanguageScreen';
import ProfileScreen from '../screens/ProfileScreen';


interface AdminNavigatorProps {
  user: User;
  onLogout: () => void;
}

type AdminScreen =
  | 'home'
  | 'dashboard'
  | 'users'
  | 'cases'
  | 'tasks'
  | 'documents'
  | 'notifications'
  | 'settings'
  | 'language'
  | 'profile';

const AdminNavigator = ({ user, onLogout }: AdminNavigatorProps) => {
  const [currentScreen, setCurrentScreen] = useState<AdminScreen>('home');
  const [currentUser, setCurrentUser] = useState<User>(user);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [navigationStack, setNavigationStack] = useState<AdminScreen[]>(['home']);
  const [cameFromSettings, setCameFromSettings] = useState(false);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    activeCases: 0,
    pendingTasks: 0,
    totalDrivers: 0,
  });

  useEffect(() => {
    loadUserFromDatabase();
    loadDashboardStats();
  }, []);

  const loadUserFromDatabase = async () => {
    try {
      const dbUser = await authService.getCurrentUser();
      if (dbUser) {
        setCurrentUser(dbUser);
      }
    } catch (error) {
      console.error('AdminNavigator: Error loading user from database:', error);
    }
  };

  const loadDashboardStats = async () => {
    try {
      setLoading(true);

      // Paralel olarak tüm verileri çek
      const [
        allCases,
        allTasks,
        allUsers
      ] = await Promise.all([
        caseService.getAllCases(),
        taskService.getAllTasks(),
        userService.getAllUsers()
      ]);

      // İstatistikleri hesapla
      const activeCases = allCases.filter(c => c.status === 'OPEN').length;
      const pendingTasks = allTasks.filter(t => t.status === 'PENDING').length;
      const totalDrivers = allUsers.filter(u => u.role === 'DRIVER').length;

      setStats({
        activeCases,
        pendingTasks,
        totalDrivers,
      });

    } catch (error) {
      console.error('AdminNavigator: Error loading dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  // Navigation functions
  const navigateToScreen = (screen: AdminScreen) => {
    setNavigationStack(prev => [...prev, screen]);
    setCurrentScreen(screen);

    // Ana sayfaya dönüldüğünde verileri yenile
    if (screen === 'home') {
      loadDashboardStats();
    }
  };

  const navigateBack = () => {
    // If we came from settings and are on language or notifications, go back to settings
    if (cameFromSettings && (currentScreen === 'language' || currentScreen === 'notifications')) {
      setCameFromSettings(false);
      setCurrentScreen('home');
      setShowSettingsModal(true);
      return;
    }

    if (navigationStack.length > 1) {
      const newStack = [...navigationStack];
      newStack.pop(); // Remove current screen
      const previousScreen = newStack[newStack.length - 1];
      setNavigationStack(newStack);
      setCurrentScreen(previousScreen);

      // Ana sayfaya dönüldüğünde verileri yenile
      if (previousScreen === 'home') {
        loadDashboardStats();
      }
    }
  };

  const resetToHome = () => {
    setNavigationStack(['home']);
    setCurrentScreen('home');
    loadDashboardStats(); // Ana sayfaya dönüldüğünde verileri yenile
  };

  const menuItems = [
    { id: 'dashboard', title: 'Dashboard', icon: 'analytics', color: '#0D3B66' },
    { id: 'users', title: 'Sürücüler', icon: 'car', color: '#2ECC71' },
    { id: 'cases', title: 'Vakalar', icon: 'folder', color: '#E74C3C' },
    { id: 'tasks', title: 'Görevler', icon: 'checkmark-circle', color: '#F39C12' },
    { id: 'documents', title: 'Belgeler', icon: 'document-text', color: '#9B59B6' },
    { id: 'settings', title: 'Ayarlar', icon: 'settings', color: '#FAA916' },
  ];

  const renderScreen = () => {
    switch (currentScreen) {
      case 'dashboard':
        return <AdminDashboardScreen user={currentUser} onNavigate={navigateToScreen} onBack={navigateBack} />;
      case 'users':
        return <UsersManagementScreen user={currentUser} onBack={navigateBack} />;
      case 'cases':
        return <CasesManagementScreen user={currentUser} onBack={navigateBack} />;
      case 'tasks':
        return <TasksManagementScreen user={currentUser} onBack={navigateBack} />;
      case 'documents':
        return <DocumentsManagementScreen user={currentUser} onBack={navigateBack} />;
      case 'notifications':
        return <NotificationsScreen onBack={navigateBack} />;
      case 'language':
        return <LanguageScreen onBack={navigateBack} />;
      case 'profile':
        return <ProfileScreen user={currentUser} onBack={navigateBack} onLogout={onLogout} />;
      default:
        return <AdminDashboardScreen user={currentUser} onNavigate={navigateToScreen} />;
    }
  };

  // If not on home, show the selected screen
  if (currentScreen !== 'home') {
    return renderScreen();
  }

  // Dashboard with navigation menu
  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <View style={styles.logoContainer}>
            <Ionicons name="shield-checkmark" size={28} color="#0D3B66" />
          </View>
          <View style={styles.welcomeContent}>
            <Text style={styles.roleTitle}>
              {currentUser.role === 'ADMIN' ? 'Yönetici' : currentUser.role === 'DRIVER' ? 'Sürücü' : 'Aile'}
            </Text>
            <Text style={styles.userName}>{currentUser.full_name}</Text>
          </View>
        </View>
        <TouchableOpacity
          style={styles.profileButton}
          onPress={() => navigateToScreen('profile')}
        >
          <Ionicons name="person-circle" size={32} color="#0D3B66" />
        </TouchableOpacity>
      </View>



      {/* Menu Grid */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Text style={styles.sectionTitle}>Yönetim Paneli</Text>

        <View style={styles.menuGrid}>
          {menuItems.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={styles.menuItem}
              onPress={() => {
                if (item.id === 'settings') {
                  setShowSettingsModal(true);
                } else {
                  navigateToScreen(item.id as AdminScreen);
                }
              }}
            >
              <View style={[styles.menuIcon, { backgroundColor: item.color }]}>
                <Ionicons name={item.icon as any} size={24} color="#FFFFFF" />
              </View>
              <Text style={styles.menuTitle}>{item.title}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Quick Stats */}
        <View style={styles.quickStats}>
          <Text style={styles.sectionTitle}>Hızlı Özet</Text>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#0D3B66" />
              <Text style={styles.loadingText}>Veriler yükleniyor...</Text>
            </View>
          ) : (
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{stats.activeCases}</Text>
                <Text style={styles.statLabel}>Aktif Vaka</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{stats.pendingTasks}</Text>
                <Text style={styles.statLabel}>Bekleyen Görev</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{stats.totalDrivers}</Text>
                <Text style={styles.statLabel}>Toplam Sürücü</Text>
              </View>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Settings Modal */}
      <Modal
        visible={showSettingsModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowSettingsModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          {/* Modal Header */}
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setShowSettingsModal(false)}
              style={styles.modalCloseButton}
            >
              <Ionicons name="close" size={24} color="#8E9297" />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Ayarlar</Text>
            <View style={styles.modalHeaderRight} />
          </View>

          {/* Modal Content */}
          <View style={styles.modalContent}>
            <Text style={styles.modalSectionTitle}>Uygulama Ayarları</Text>

            <View style={styles.settingsOptions}>
              <TouchableOpacity
                style={styles.settingsOption}
                onPress={() => {
                  setShowSettingsModal(false);
                  setCameFromSettings(true);
                  navigateToScreen('language');
                }}
              >
                <View style={styles.settingsOptionLeft}>
                  <View style={[styles.settingsOptionIcon, { backgroundColor: '#FAA916' }]}>
                    <Ionicons name="language" size={20} color="#FFFFFF" />
                  </View>
                  <View style={styles.settingsOptionInfo}>
                    <Text style={styles.settingsOptionTitle}>Dil</Text>
                    <Text style={styles.settingsOptionDescription}>Uygulama dilini değiştir</Text>
                  </View>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#8E9297" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.settingsOption}
                onPress={() => {
                  setShowSettingsModal(false);
                  setCameFromSettings(true);
                  navigateToScreen('notifications');
                }}
              >
                <View style={styles.settingsOptionLeft}>
                  <View style={[styles.settingsOptionIcon, { backgroundColor: '#2ECC71' }]}>
                    <Ionicons name="notifications" size={20} color="#FFFFFF" />
                  </View>
                  <View style={styles.settingsOptionInfo}>
                    <Text style={styles.settingsOptionTitle}>Bildirimler</Text>
                    <Text style={styles.settingsOptionDescription}>Bildirim ayarlarını yönet</Text>
                  </View>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#8E9297" />
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 16 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  logoContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(13, 59, 102, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  welcomeContent: {
    flex: 1,
  },
  roleTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8E9297',
    marginBottom: 2,
  },
  userName: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1C1C1E',
  },
  profileButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginTop: 24,
    marginBottom: 16,
  },
  menuGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  menuItem: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  menuIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  menuTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
    textAlign: 'center',
  },
  quickStats: {
    marginTop: 8,
    marginBottom: 32,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#E1E8ED',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: '#0D3B66',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#8E9297',
    textAlign: 'center',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#8E9297',
  },
  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  modalCloseButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  modalHeaderRight: {
    width: 40,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 24,
  },
  modalSectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 24,
  },
  settingsOptions: {
    gap: 12,
  },
  settingsOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  settingsOptionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingsOptionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  settingsOptionInfo: {
    flex: 1,
  },
  settingsOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  settingsOptionDescription: {
    fontSize: 14,
    color: '#8E9297',
  },
});

export default AdminNavigator;
