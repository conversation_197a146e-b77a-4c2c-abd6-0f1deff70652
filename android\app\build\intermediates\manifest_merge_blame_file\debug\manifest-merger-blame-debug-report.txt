1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ditib.funeralapp"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:4:3-75
11-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:2:3-64
12-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:3:3-77
13-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:3:20-75
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:5:3-63
14-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:5:20-61
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:6:3-78
15-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:6:20-76
16
17    <queries>
17-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:7:3-13:13
18        <intent>
18-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:8:5-12:14
19            <action android:name="android.intent.action.VIEW" />
19-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:9:7-58
19-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:9:15-56
20
21            <category android:name="android.intent.category.BROWSABLE" />
21-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:10:7-67
21-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:10:17-65
22
23            <data android:scheme="https" />
23-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:11:7-37
23-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:11:13-35
24        </intent>
25        <!-- Query open documents -->
26        <intent>
26-->[:expo-file-system] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:9-17:18
27            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
27-->[:expo-file-system] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-79
27-->[:expo-file-system] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:21-76
28        </intent>
29    </queries>
30
31    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
31-->[com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab4e4a0c6fa3458e0b972f8603aac561\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:5-76
31-->[com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab4e4a0c6fa3458e0b972f8603aac561\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:22-73
32
33    <permission
33-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\97dafb02ce1c76f135d92386cd4ea378\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
34        android:name="com.ditib.funeralapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
34-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\97dafb02ce1c76f135d92386cd4ea378\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
35        android:protectionLevel="signature" />
35-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\97dafb02ce1c76f135d92386cd4ea378\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
36
37    <uses-permission android:name="com.ditib.funeralapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
37-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\97dafb02ce1c76f135d92386cd4ea378\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
37-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\97dafb02ce1c76f135d92386cd4ea378\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
38
39    <application
39-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:14:3-32:17
40        android:name="com.ditib.funeralapp.MainApplication"
40-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:14:16-47
41        android:allowBackup="true"
41-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:14:162-188
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\97dafb02ce1c76f135d92386cd4ea378\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
43        android:debuggable="true"
44        android:extractNativeLibs="false"
45        android:icon="@mipmap/ic_launcher"
45-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:14:81-115
46        android:label="@string/app_name"
46-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:14:48-80
47        android:roundIcon="@mipmap/ic_launcher_round"
47-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:14:116-161
48        android:theme="@style/AppTheme"
48-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:14:189-220
49        android:usesCleartextTraffic="true" >
49-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\debug\AndroidManifest.xml:6:18-53
50        <meta-data
50-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:15:5-83
51            android:name="expo.modules.updates.ENABLED"
51-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:15:16-59
52            android:value="false" />
52-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:15:60-81
53        <meta-data
53-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:16:5-93
54            android:name="expo.modules.updates.EXPO_SDK_VERSION"
54-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:16:16-68
55            android:value="50.0.0" />
55-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:16:69-91
56        <meta-data
56-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:17:5-105
57            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
57-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:17:16-80
58            android:value="ALWAYS" />
58-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:17:81-103
59        <meta-data
59-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:18:5-99
60            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
60-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:18:16-79
61            android:value="0" />
61-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:18:80-97
62
63        <activity
63-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:19:5-30:16
64            android:name="com.ditib.funeralapp.MainActivity"
64-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:19:15-43
65            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
65-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:19:44-134
66            android:exported="true"
66-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:19:256-279
67            android:launchMode="singleTask"
67-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:19:135-166
68            android:screenOrientation="portrait"
68-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:19:280-316
69            android:theme="@style/Theme.App.SplashScreen"
69-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:19:210-255
70            android:windowSoftInputMode="adjustResize" >
70-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:19:167-209
71            <intent-filter>
71-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:20:7-23:23
72                <action android:name="android.intent.action.MAIN" />
72-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:21:9-60
72-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:21:17-58
73
74                <category android:name="android.intent.category.LAUNCHER" />
74-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:22:9-68
74-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:22:19-66
75            </intent-filter>
76            <intent-filter>
76-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:24:7-29:23
77                <action android:name="android.intent.action.VIEW" />
77-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:9:7-58
77-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:9:15-56
78
79                <category android:name="android.intent.category.DEFAULT" />
79-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:26:9-67
79-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:26:19-65
80                <category android:name="android.intent.category.BROWSABLE" />
80-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:10:7-67
80-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:10:17-65
81
82                <data android:scheme="com.ditib.funeralapp" />
82-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:11:7-37
82-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:11:13-35
83            </intent-filter>
84        </activity>
85        <activity
85-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:31:5-106
86            android:name="com.facebook.react.devsupport.DevSettingsActivity"
86-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:31:15-79
87            android:exported="false" />
87-->C:\Users\<USER>\VSCode\MobileProjects\funeral-app\android\app\src\main\AndroidManifest.xml:31:80-104
88
89        <provider
89-->[:expo-file-system] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:9-30:20
90            android:name="expo.modules.filesystem.FileSystemFileProvider"
90-->[:expo-file-system] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-74
91            android:authorities="com.ditib.funeralapp.FileSystemFileProvider"
91-->[:expo-file-system] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-74
92            android:exported="false"
92-->[:expo-file-system] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-37
93            android:grantUriPermissions="true" >
93-->[:expo-file-system] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:13-47
94            <meta-data
94-->[:expo-file-system] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-29:70
95                android:name="android.support.FILE_PROVIDER_PATHS"
95-->[:expo-file-system] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:28:17-67
96                android:resource="@xml/file_system_provider_paths" />
96-->[:expo-file-system] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-67
97        </provider>
98
99        <meta-data
99-->[:expo-modules-core] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-11:89
100            android:name="org.unimodules.core.AppLoader#react-native-headless"
100-->[:expo-modules-core] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-79
101            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
101-->[:expo-modules-core] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-86
102        <meta-data
102-->[:expo-modules-core] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-15:45
103            android:name="com.facebook.soloader.enabled"
103-->[:expo-modules-core] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-57
104            android:value="true" />
104-->[:expo-modules-core] C:\Users\<USER>\VSCode\MobileProjects\funeral-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-33
105
106        <provider
106-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4dbb3f58a9fd53a0c4eaa8ffb9d2a519\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
107            android:name="androidx.startup.InitializationProvider"
107-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4dbb3f58a9fd53a0c4eaa8ffb9d2a519\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
108            android:authorities="com.ditib.funeralapp.androidx-startup"
108-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4dbb3f58a9fd53a0c4eaa8ffb9d2a519\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
109            android:exported="false" >
109-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4dbb3f58a9fd53a0c4eaa8ffb9d2a519\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
110            <meta-data
110-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4dbb3f58a9fd53a0c4eaa8ffb9d2a519\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
111                android:name="androidx.emoji2.text.EmojiCompatInitializer"
111-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4dbb3f58a9fd53a0c4eaa8ffb9d2a519\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
112                android:value="androidx.startup" />
112-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4dbb3f58a9fd53a0c4eaa8ffb9d2a519\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
113            <meta-data
113-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8161456b63a4fc8ccb1191ca896117e0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
114                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
114-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8161456b63a4fc8ccb1191ca896117e0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
115                android:value="androidx.startup" />
115-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8161456b63a4fc8ccb1191ca896117e0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
116        </provider>
117    </application>
118
119</manifest>
