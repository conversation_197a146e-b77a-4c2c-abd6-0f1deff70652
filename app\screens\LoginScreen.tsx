import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { authService, User } from '../services/authService';
import { Ionicons } from '@expo/vector-icons';
import FirstTimeLoginScreen from './FirstTimeLoginScreenNew';
import ForgotPasswordScreen from './ForgotPasswordScreen';
import ZSULogo from '../components/ZSULogo';
import { useAlertHelpers } from '../hooks/useAlert';
import { useTranslation } from '../i18n/useTranslation';
import { emailService } from '../services/emailService';

interface LoginScreenProps {
  onLogin: (user: User) => void;
  onShowFirstTimeLogin: () => void;
}

const LoginScreen = ({ onLogin }: LoginScreenProps) => {
  const { t } = useTranslation();
  const [showFirstTimeLogin, setShowFirstTimeLogin] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isMagicLinkSent, setIsMagicLinkSent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const { showError, showInfo, showSuccess, showConfirm } = useAlertHelpers();

  // Component mount olduğunda bağlantıyı test et
  useEffect(() => {
    const testConnection = async () => {
      console.log('LoginScreen: Testing connection on mount...');
      const isConnected = await authService.testConnection();
      console.log('LoginScreen: Connection test result:', isConnected);
    };
    testConnection();
  }, []);

  // Handle login
  const handleLogin = async () => {
    if (!email || !password) {
      showError(t('common.error'), t('alerts.fillAllFields'));
      return;
    }

    setIsLoading(true);

    try {
      // Önce bağlantıyı test et
      console.log('LoginScreen: Testing connection before login...');
      const isConnected = await authService.testConnection();
      if (!isConnected) {
        showError(t('alerts.connectionError'), t('alerts.serverConnectionFailed'));
        setIsLoading(false);
        return;
      }

      console.log('LoginScreen: Connection OK, proceeding with login...');

      const response = await authService.signInWithPassword(email, password);

      if (response.success && response.user) {
        console.log('Login successful:', response.user);
        console.log('Calling onLogin function...');
        onLogin(response.user);
        console.log('onLogin function called');
      } else {
        console.log('Login failed:', response.message);

        // Merkezi sistem ile uyumlu hata yönetimi
        if (response.needsAccountCreation) {
          // Kullanıcı users tablosunda var ama auth.users'da yok - şifre oluşturması gerekiyor
          showConfirm(
            'Şifre Oluşturma Gerekli',
            'Bu e-posta adresi için henüz şifre oluşturulmamış. Şifre oluşturmak için Hesap Oluştur sayfasına gitmek ister misiniz?',
            () => setShowFirstTimeLogin(true), // onConfirm
            () => {}, // onCancel
            'Hesap Oluştur', // confirmText
            'İptal' // cancelText
          );
        } else {
          // Diğer durumlar (yanlış şifre, kullanıcı bulunamadı, vb.)
          showInfo(t('common.info'), response.message || response.error || t('auth.loginFailed'));
        }
      }
    } catch (error) {
      showError(t('common.error'), t('alerts.unexpectedError'));
    } finally {
      setIsLoading(false);
    }
  };

  // Handle magic link
  const handleMagicLink = async () => {
    if (!email) {
      showError(t('common.error'), t('alerts.enterEmail'));
      return;
    }

    setIsLoading(true);

    try {
      const response = await authService.signInWithMagicLink(email);

      if (response.success) {
        setIsMagicLinkSent(true);
        showSuccess(
          t('alerts.magicLinkSent'),
          t('alerts.checkEmailForMagicLink')
        );
      } else {
        showInfo(t('common.info'), response.message || t('alerts.magicLinkFailed'));
      }
    } catch (error) {
      showError(t('common.error'), t('alerts.unexpectedError'));
    } finally {
      setIsLoading(false);
    }
  };

  // Conditional rendering for different screens
  if (showFirstTimeLogin) {
    return (
      <FirstTimeLoginScreen
        onSuccess={() => {
          setShowFirstTimeLogin(false);
          // Magic link ile giriş yapıldıktan sonra ana uygulamaya yönlendir
        }}
        onBack={() => setShowFirstTimeLogin(false)}
      />
    );
  }

  if (showForgotPassword) {
    return (
      <ForgotPasswordScreen
        onBack={() => setShowForgotPassword(false)}
      />
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <ZSULogo width={160} height={60} />
            </View>
            <Text style={styles.subtitle}>Cenaze Hizmetleri Yönetim Sistemi</Text>
          </View>

          {/* Login Form */}
          <View style={styles.form}>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>E-posta</Text>
              <TextInput
                style={styles.input}
                placeholder={emailService.getPlaceholderEmail()}
                placeholderTextColor="#8E9297"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="off"
                autoCorrect={false}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Şifre</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={styles.passwordInput}
                  placeholder="••••••••"
                  placeholderTextColor="#8E9297"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                  autoComplete="off"
                  autoCorrect={false}
                  autoCapitalize="none"
                />
                <TouchableOpacity
                  style={styles.eyeButton}
                  onPress={() => setShowPassword(!showPassword)}
                  activeOpacity={0.7}
                >
                  <Ionicons
                    name={showPassword ? 'eye-off' : 'eye'}
                    size={20}
                    color="#8E9297"
                  />
                </TouchableOpacity>
              </View>
            </View>

            <TouchableOpacity
              style={[styles.loginButton, isLoading && styles.buttonDisabled]}
              onPress={handleLogin}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="#FFFFFF" />
              ) : (
                <Text style={styles.loginButtonText}>Giriş Yap</Text>
              )}
            </TouchableOpacity>



            {/* Magic Link geçici olarak gizlendi - redirect sorunu nedeniyle */}
            {false && (
              <TouchableOpacity
                style={[
                  styles.magicButton,
                  isLoading && styles.buttonDisabled,
                  isMagicLinkSent && styles.magicButtonSent
                ]}
                onPress={handleMagicLink}
                disabled={isLoading}
              >
                {isLoading ? (
                  <ActivityIndicator color="#FFFFFF" />
                ) : (
                  <Text style={styles.magicButtonText}>
                    {isMagicLinkSent ? 'Magic Link Gönderildi ✓' : 'Magic Link Gönder'}
                  </Text>
                )}
              </TouchableOpacity>
            )}



            <TouchableOpacity
              style={styles.firstTimeButton}
              onPress={() => setShowFirstTimeLogin(true)}
            >
              <Text style={styles.firstTimeButtonText}>Hesap Oluştur</Text>
            </TouchableOpacity>
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <TouchableOpacity onPress={() => setShowForgotPassword(true)}>
              <Text style={styles.forgotPassword}>Şifremi Unuttum</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: Platform.OS === 'ios' ? 20 : 60,
    paddingBottom: 40,
    maxWidth: 400,
    alignSelf: 'center',
    width: '100%',
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  subtitle: {
    fontSize: 14,
    color: '#8E9297',
    textAlign: 'center',
    marginTop: 8,
  },
  form: {
    marginBottom: 32,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  input: {
    height: 48,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E1E8ED',
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 14,
    color: '#1C1C1E',
  },
  passwordContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  passwordInput: {
    flex: 1,
    height: 48,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E1E8ED',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingRight: 50,
    fontSize: 14,
    color: '#1C1C1E',
  },
  eyeButton: {
    position: 'absolute',
    right: 16,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  loginButton: {
    height: 48,
    backgroundColor: '#0D3B66',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },

  magicButton: {
    height: 48,
    backgroundColor: '#FAA916',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  magicButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  footer: {
    alignItems: 'center',
  },
  forgotPassword: {
    fontSize: 12,
    color: '#0D3B66',
  },
  inputError: {
    borderColor: '#E74C3C',
    borderWidth: 2,
  },
  errorText: {
    fontSize: 12,
    color: '#E74C3C',
    marginTop: 4,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  magicButtonSent: {
    backgroundColor: '#2ECC71',
  },
  firstTimeButton: {
    height: 48,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#0D3B66',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  firstTimeButtonText: {
    color: '#0D3B66',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default LoginScreen;
