import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
  Platform,
  StatusBar,
  Modal,
  TextInput,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User } from '../../services/authService';
import { caseService } from '../../services/caseService';
import { taskService } from '../../services/taskService';
import { userService } from '../../services/userService';
import { useTranslation } from 'react-i18next';

interface AdminDashboardScreenProps {
  user: User;
  onNavigate: (screen: string) => void;
  onBack?: () => void;
}

const AdminDashboardScreen = ({ user, onNavigate, onBack }: AdminDashboardScreenProps) => {
  const { t } = useTranslation();
  const screenWidth = Dimensions.get('window').width;
  const isSmallScreen = screenWidth < 400;

  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    activeCases: 0,
    pendingTasks: 0,
    totalDrivers: 0,
    completedThisMonth: 0,
  });
  const [urgentTasks, setUrgentTasks] = useState<any[]>([]);
  const [recentActivities, setRecentActivities] = useState<any[]>([]);

  // Quick Notification Modal States
  const [showNotificationModal, setShowNotificationModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [selectedTargetGroup, setSelectedTargetGroup] = useState<string>('ALL');
  const [customMessage, setCustomMessage] = useState<string>('');
  const [sendingNotification, setSendingNotification] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      console.log('AdminDashboard: Loading dashboard data...');

      // Paralel olarak tüm verileri çek
      const [
        allCases,
        allTasks,
        driverStats,
        urgentTasksData
      ] = await Promise.all([
        caseService.getAllCases(),
        taskService.getAllTasks(),
        userService.getDriverStats(),
        taskService.getUrgentTasks(5)
      ]);

      console.log('AdminDashboard: Data loaded successfully');
      console.log('- Cases:', allCases?.length || 0);
      console.log('- Tasks:', allTasks?.length || 0);
      console.log('- Urgent tasks:', urgentTasksData?.length || 0);

      // İstatistikleri hesapla
      const activeCases = allCases.filter(c => c.status === 'OPEN').length;
      const pendingTasks = allTasks.filter(t => t.status === 'ASSIGNED').length;

      // Bu ay tamamlanan vakaları hesapla
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const completedThisMonth = allCases.filter(c => {
        if (c.status !== 'CLOSED') return false;
        const createdDate = new Date(c.created_at);
        return createdDate.getMonth() === currentMonth && createdDate.getFullYear() === currentYear;
      }).length;

      setStats({
        activeCases,
        pendingTasks,
        totalDrivers: driverStats.total,
        completedThisMonth,
      });

      setUrgentTasks(urgentTasksData);

      // Son aktiviteleri oluştur (basitleştirilmiş)
      const recentTaskActivities = allTasks
        .slice(0, 3)
        .map(task => ({
          id: `task-${task.id}`,
          title: task.status === 'DONE' ? 'Görev tamamlandı' : 'Yeni görev oluşturuldu',
          description: `${task.task_type === 'PICK_UP_FROM_MORGUE' ? 'Morgdan Alma' :
                         task.task_type === 'TO_AIRPORT' ? 'Havaalanına Götürme' :
                         task.task_type === 'TO_CONSULATE' ? 'Konsolosluğa Götürme' :
                         task.task_type === 'DELIVERED' ? 'Teslim Edildi' : task.task_type} - Görev ID: ${task.id.substring(0, 8)}...`,
          time: getTimeAgo(task.scheduled_at || new Date().toISOString()),
          icon: task.status === 'DONE' ? 'checkmark-circle' : 'add-circle',
          color: task.status === 'DONE' ? '#2ECC71' : '#F39C12',
        }));

      setRecentActivities(recentTaskActivities);

    } catch (error) {
      console.error('Dashboard data loading error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTimeAgo = (dateString: string): string => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
      return `${diffInMinutes} dakika önce`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)} saat önce`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)} gün önce`;
    }
  };

  // Notification Templates
  const notificationTemplates = [
    {
      id: 'EMERGENCY',
      title: 'Acil Durum',
      message: 'Acil durum bildirimi: Lütfen derhal iletişime geçin.',
      icon: 'warning',
      color: '#E74C3C',
    },
    {
      id: 'MAINTENANCE',
      title: 'Sistem Bakımı',
      message: 'Sistem bakımı nedeniyle hizmet geçici olarak kesintiye uğrayabilir.',
      icon: 'construct',
      color: '#F39C12',
    },
    {
      id: 'ANNOUNCEMENT',
      title: 'Önemli Duyuru',
      message: 'Önemli bir duyuru var. Lütfen kontrol edin.',
      icon: 'megaphone',
      color: '#2ECC71',
    },
    {
      id: 'CUSTOM',
      title: 'Özel Mesaj',
      message: '',
      icon: 'create',
      color: '#9B59B6',
    },
  ];

  const targetGroups = [
    { id: 'ALL', title: 'Tüm Kullanıcılar', icon: 'people' },
    { id: 'DRIVERS', title: 'Sadece Sürücüler', icon: 'car' },
    { id: 'FAMILIES', title: 'Sadece Aileler', icon: 'home' },
  ];

  // Quick Notification Functions
  const handleQuickNotification = () => {
    setShowNotificationModal(true);
  };

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    const template = notificationTemplates.find(t => t.id === templateId);
    if (template && template.id !== 'CUSTOM') {
      setCustomMessage(template.message);
    } else {
      setCustomMessage('');
    }
  };

  const handleSendNotification = async () => {
    if (!selectedTemplate) {
      Alert.alert(t('common.error'), t('alerts.selectTemplate'));
      return;
    }

    if (selectedTemplate === 'CUSTOM' && !customMessage.trim()) {
      Alert.alert(t('common.error'), t('alerts.writeMessage'));
      return;
    }

    try {
      setSendingNotification(true);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      const template = notificationTemplates.find(t => t.id === selectedTemplate);
      const targetGroup = targetGroups.find(g => g.id === selectedTargetGroup);

      Alert.alert(
        t('alerts.notificationSent'),
        `"${template?.title}" bildirimi ${targetGroup?.title.toLowerCase()} grubuna başarıyla gönderildi.`,
        [
          {
            text: t('common.ok'),
            onPress: () => {
              setShowNotificationModal(false);
              setSelectedTemplate('');
              setSelectedTargetGroup('ALL');
              setCustomMessage('');
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert(t('common.error'), t('alerts.notificationError'));
    } finally {
      setSendingNotification(false);
    }
  };

  const quickStats = [
    { title: t('dashboard.activeCases'), value: stats.activeCases.toString(), color: '#E74C3C', icon: 'folder' },
    { title: 'Bekleyen Görevler', value: stats.pendingTasks.toString(), color: '#F39C12', icon: 'time' },
    { title: 'Toplam Sürücü', value: stats.totalDrivers.toString(), color: '#2ECC71', icon: 'car' },
    { title: 'Bu Ay Tamamlanan', value: stats.completedThisMonth.toString(), color: '#9B59B6', icon: 'checkmark-circle' },
  ];

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0D3B66" />
          <Text style={styles.loadingText}>Dashboard yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      {onBack && (
        <View style={styles.header}>
          <TouchableOpacity onPress={onBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#0D3B66" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Dashboard</Text>
          <View style={styles.headerRight} />
        </View>
      )}

      <ScrollView showsVerticalScrollIndicator={false}>

        {/* Quick Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Genel Durum</Text>
          <View style={styles.statsGrid}>
            {quickStats.map((stat, index) => (
              <View key={index} style={[styles.statCard, isSmallScreen && styles.statCardSmall]}>
                <View style={[styles.statIcon, { backgroundColor: stat.color }]}>
                  <Ionicons name={stat.icon as any} size={isSmallScreen ? 16 : 20} color="#FFFFFF" />
                </View>
                <Text style={[styles.statValue, isSmallScreen && styles.statValueSmall]}>{stat.value}</Text>
                <Text style={[styles.statTitle, isSmallScreen && styles.statTitleSmall]}>{stat.title}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Urgent Tasks */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Acil Görevler</Text>
            <TouchableOpacity onPress={() => onNavigate('tasks')}>
              <Text style={styles.seeAllText}>Tümünü Gör</Text>
            </TouchableOpacity>
          </View>
          {urgentTasks.length > 0 ? urgentTasks.map((task) => (
            <View key={task.id} style={styles.urgentTaskCard}>
              <View style={styles.urgentTaskHeader}>
                <View style={styles.urgentTaskInfo}>
                  <Text style={styles.urgentTaskTitle}>
                    {task.task_type === 'PICK_UP_FROM_MORGUE' ? 'Morgdan Alma' :
                     task.task_type === 'TO_AIRPORT' ? 'Havaalanına Götürme' :
                     task.task_type === 'TO_CONSULATE' ? 'Konsolosluğa Götürme' :
                     task.task_type === 'DELIVERED' ? 'Teslim Edildi' : task.task_type}
                  </Text>
                  <Text style={styles.urgentTaskCase}>
                    Görev ID: {task.id.substring(0, 8)}...
                  </Text>
                </View>
                <View style={styles.urgentTaskMeta}>
                  <View style={[
                    styles.priorityBadge,
                    { backgroundColor: task.status === 'PENDING' ? '#E74C3C' : '#F39C12' }
                  ]}>
                    <Text style={styles.priorityText}>
                      {task.status === 'PENDING' ? 'Bekliyor' : task.status === 'ACTIVE' ? 'Aktif' : 'Tamamlandı'}
                    </Text>
                  </View>
                  {task.scheduled_at && (
                    <Text style={styles.dueTime}>
                      {new Date(task.scheduled_at).toLocaleDateString('tr-TR')}
                    </Text>
                  )}
                </View>
              </View>
            </View>
          )) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>Acil görev bulunmuyor</Text>
            </View>
          )}
        </View>

        {/* Recent Activities */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Son Aktiviteler</Text>
          {recentActivities.map((activity) => (
            <View key={activity.id} style={styles.activityCard}>
              <View style={[styles.activityIcon, { backgroundColor: activity.color }]}>
                <Ionicons name={activity.icon as any} size={16} color="#FFFFFF" />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>{activity.title}</Text>
                <Text style={styles.activityDescription}>{activity.description}</Text>
                <Text style={styles.activityTime}>{activity.time}</Text>
              </View>
            </View>
          ))}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Hızlı İşlemler</Text>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity
              style={[styles.quickActionCard, isSmallScreen && styles.quickActionCardSmall]}
              onPress={() => onNavigate('cases')}
            >
              <Ionicons name="add-circle" size={isSmallScreen ? 20 : 24} color="#2ECC71" />
              <Text style={[styles.quickActionText, isSmallScreen && styles.quickActionTextSmall]}>Yeni Vaka</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.quickActionCard, isSmallScreen && styles.quickActionCardSmall]}
              onPress={() => onNavigate('users')}
            >
              <Ionicons name="person-add" size={isSmallScreen ? 20 : 24} color="#0D3B66" />
              <Text style={[styles.quickActionText, isSmallScreen && styles.quickActionTextSmall]}>Sürücü Ekle</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.quickActionCard, isSmallScreen && styles.quickActionCardSmall]}
              onPress={() => onNavigate('tasks')}
            >
              <Ionicons name="list" size={isSmallScreen ? 20 : 24} color="#F39C12" />
              <Text style={[styles.quickActionText, isSmallScreen && styles.quickActionTextSmall]}>Görev Ata</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.quickActionCard, isSmallScreen && styles.quickActionCardSmall]}
              onPress={handleQuickNotification}
            >
              <Ionicons name="send" size={isSmallScreen ? 20 : 24} color="#9B59B6" />
              <Text style={[styles.quickActionText, isSmallScreen && styles.quickActionTextSmall]}>Bildirim Gönder</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Quick Notification Modal */}
      <Modal
        visible={showNotificationModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowNotificationModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          {/* Modal Header */}
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setShowNotificationModal(false)}
              style={styles.modalCloseButton}
            >
              <Ionicons name="close" size={24} color="#8E9297" />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Hızlı Bildirim Gönder</Text>
            <View style={styles.modalHeaderRight} />
          </View>

          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            {/* Template Selection */}
            <View style={styles.modalSection}>
              <Text style={styles.modalSectionTitle}>Şablon Seçin</Text>
              <View style={styles.templateGrid}>
                {notificationTemplates.map((template) => (
                  <TouchableOpacity
                    key={template.id}
                    style={[
                      styles.templateCard,
                      selectedTemplate === template.id && styles.templateCardSelected
                    ]}
                    onPress={() => handleTemplateSelect(template.id)}
                  >
                    <View style={[styles.templateIcon, { backgroundColor: template.color }]}>
                      <Ionicons name={template.icon as any} size={20} color="#FFFFFF" />
                    </View>
                    <Text style={[
                      styles.templateTitle,
                      selectedTemplate === template.id && styles.templateTitleSelected
                    ]}>
                      {template.title}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Target Group Selection */}
            <View style={styles.modalSection}>
              <Text style={styles.modalSectionTitle}>Hedef Grup</Text>
              <View style={styles.targetGroupContainer}>
                {targetGroups.map((group) => (
                  <TouchableOpacity
                    key={group.id}
                    style={[
                      styles.targetGroupButton,
                      selectedTargetGroup === group.id && styles.targetGroupButtonSelected
                    ]}
                    onPress={() => setSelectedTargetGroup(group.id)}
                  >
                    <Ionicons
                      name={group.icon as any}
                      size={16}
                      color={selectedTargetGroup === group.id ? '#FFFFFF' : '#8E9297'}
                    />
                    <Text style={[
                      styles.targetGroupText,
                      selectedTargetGroup === group.id && styles.targetGroupTextSelected
                    ]}>
                      {group.title}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Message Input */}
            <View style={styles.modalSection}>
              <Text style={styles.modalSectionTitle}>
                {selectedTemplate === 'CUSTOM' ? 'Mesajınızı Yazın' : 'Mesaj Önizleme'}
              </Text>
              <TextInput
                style={[
                  styles.messageInput,
                  selectedTemplate !== 'CUSTOM' && styles.messageInputReadonly
                ]}
                multiline
                numberOfLines={4}
                placeholder={selectedTemplate === 'CUSTOM' ? t('alerts.writeMessageHere') : ''}
                placeholderTextColor="#B8C5D1"
                value={customMessage}
                onChangeText={setCustomMessage}
                editable={selectedTemplate === 'CUSTOM'}
              />
            </View>

            {/* Send Button */}
            <TouchableOpacity
              style={[
                styles.sendButton,
                (!selectedTemplate || (selectedTemplate === 'CUSTOM' && !customMessage.trim()) || sendingNotification) && styles.sendButtonDisabled
              ]}
              onPress={handleSendNotification}
              disabled={!selectedTemplate || (selectedTemplate === 'CUSTOM' && !customMessage.trim()) || sendingNotification}
            >
              {sendingNotification ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <>
                  <Ionicons name="send" size={20} color="#FFFFFF" />
                  <Text style={styles.sendButtonText}>Bildirim Gönder</Text>
                </>
              )}
            </TouchableOpacity>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  headerRight: {
    width: 40,
  },
  section: {
    paddingHorizontal: 24,
    marginTop: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  seeAllText: {
    fontSize: 14,
    color: '#0D3B66',
    fontWeight: '500',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    maxWidth: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statCardSmall: {
    padding: 16,
    borderRadius: 12,
    minWidth: '45%',
    maxWidth: '47%',
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  statValue: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  statValueSmall: {
    fontSize: 24,
  },
  statTitle: {
    fontSize: 12,
    color: '#8E9297',
    textAlign: 'center',
    lineHeight: 16,
  },
  statTitleSmall: {
    fontSize: 11,
    lineHeight: 14,
  },
  urgentTaskCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E1E8ED',
    borderLeftWidth: 4,
    borderLeftColor: '#E74C3C',
  },
  urgentTaskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  urgentTaskInfo: {
    flex: 1,
  },
  urgentTaskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  urgentTaskCase: {
    fontSize: 14,
    color: '#8E9297',
  },
  urgentTaskMeta: {
    alignItems: 'flex-end',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginBottom: 4,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  dueTime: {
    fontSize: 12,
    color: '#E74C3C',
    fontWeight: '500',
  },
  activityCard: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  activityDescription: {
    fontSize: 13,
    color: '#8E9297',
    marginBottom: 4,
  },
  activityTime: {
    fontSize: 12,
    color: '#B8C5D1',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 16,
    marginBottom: 32,
    gap: 12,
  },
  quickActionCard: {
    flex: 1,
    minWidth: '45%',
    maxWidth: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  quickActionCardSmall: {
    padding: 16,
    minWidth: '45%',
    maxWidth: '47%',
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 18,
  },
  quickActionTextSmall: {
    fontSize: 12,
    lineHeight: 16,
    marginTop: 6,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#8E9297',
    textAlign: 'center',
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 14,
    color: '#8E9297',
    textAlign: 'center',
  },
  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  modalCloseButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  modalHeaderRight: {
    width: 40,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  modalSection: {
    marginTop: 24,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 16,
  },
  templateGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  templateCard: {
    flex: 1,
    minWidth: '45%',
    maxWidth: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E1E8ED',
  },
  templateCardSelected: {
    borderColor: '#9B59B6',
    backgroundColor: '#F8F5FF',
  },
  templateIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  templateTitle: {
    fontSize: 12,
    fontWeight: '500',
    color: '#8E9297',
    textAlign: 'center',
  },
  templateTitleSelected: {
    color: '#9B59B6',
    fontWeight: '600',
  },
  targetGroupContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  targetGroupButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  targetGroupButtonSelected: {
    backgroundColor: '#9B59B6',
    borderColor: '#9B59B6',
  },
  targetGroupText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#8E9297',
    marginLeft: 6,
  },
  targetGroupTextSelected: {
    color: '#FFFFFF',
  },
  messageInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    fontSize: 14,
    color: '#1C1C1E',
    borderWidth: 1,
    borderColor: '#E1E8ED',
    textAlignVertical: 'top',
    minHeight: 100,
  },
  messageInputReadonly: {
    backgroundColor: '#F5F8FA',
    color: '#8E9297',
  },
  sendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#9B59B6',
    paddingVertical: 16,
    borderRadius: 12,
    marginTop: 32,
    marginBottom: 40,
  },
  sendButtonDisabled: {
    backgroundColor: '#B8C5D1',
  },
  sendButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 8,
  },
});

export default AdminDashboardScreen;
