import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { documentService, Document } from '../services/documentService';
import { useAlertHelpers } from '../hooks/useAlert';
import { useTranslation } from 'react-i18next';

interface DocumentListProps {
  caseId: string;
  currentUserId: string;
  refreshTrigger?: number;
}

const DocumentList: React.FC<DocumentListProps> = ({
  caseId,
  currentUserId,
  refreshTrigger = 0,
}) => {
  const { t } = useTranslation();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);

  const { showError, showSuccess, showConfirm } = useAlertHelpers();

  useEffect(() => {
    fetchDocuments();
  }, [caseId, refreshTrigger]);

  const fetchDocuments = async () => {
    setLoading(true);
    try {
      const result = await documentService.getDocumentsByCase(caseId);

      if (result.success && result.documents) {
        setDocuments(result.documents);
      } else {
        console.error('DocumentList: Fetch error:', result.error);
      }
    } catch (error) {
      console.error('DocumentList: Fetch unexpected error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDocumentPress = async (document: Document) => {
    try {
      const url = await documentService.getDocumentUrl(document.storage_path);

      if (url) {
        const supported = await Linking.canOpenURL(url);

        if (supported) {
          await Linking.openURL(url);
        } else {
          Alert.alert(t('common.error'), t('alerts.fileTypeNotSupported'));
        }
      } else {
        Alert.alert(t('common.error'), t('alerts.fileUrlNotFound'));
      }
    } catch (error) {
      console.error('DocumentList: Open document error:', error);
      Alert.alert(t('common.error'), t('alerts.fileOpenFailed'));
    }
  };

  const handleVerifyDocument = async (document: Document) => {
    showConfirm(
      'Belge Doğrulama',
      'Bu belgeyi doğrulamak istediğinizden emin misiniz?',
      async () => {
        const success = await documentService.verifyDocument(document.id, currentUserId);

        if (success) {
          showSuccess(t('common.success'), t('alerts.documentVerified'));
          fetchDocuments(); // Listeyi yenile
        } else {
          showError(t('common.error'), t('alerts.documentVerificationFailed'));
        }
      },
      undefined,
      'Doğrula',
      'İptal'
    );
  };

  const getDocumentIcon = (docType: string): string => {
    const icons: Record<string, string> = {
      'DEATH_CERT': 'document-text',
      'LEICHENPASS': 'airplane',
      'FORMUL_C': 'document',
      'FLIGHT_WAYBILL': 'send',
      'PHOTO': 'camera',
      'ID_DOCUMENT': 'card',
      'MEDICAL_REPORT': 'medical',
      'OTHER': 'folder',
    };
    return icons[docType] || 'document';
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderDocumentItem = ({ item }: { item: Document }) => (
    <TouchableOpacity
      style={styles.documentCard}
      onPress={() => handleDocumentPress(item)}
    >
      <View style={styles.documentHeader}>
        <View style={styles.documentInfo}>
          <View style={styles.documentIconContainer}>
            <Ionicons
              name={getDocumentIcon(item.doc_type) as any}
              size={20}
              color="#0D3B66"
            />
          </View>
          <View style={styles.documentDetails}>
            <Text style={styles.documentType}>
              {documentService.getDocumentTypeLabel(item.doc_type)}
            </Text>
            <Text style={styles.documentDate}>
              {formatDate(item.created_at)}
            </Text>
            {item.uploader && (
              <Text style={styles.documentUploader}>
                Yükleyen: {item.uploader.full_name}
              </Text>
            )}
          </View>
        </View>

        <View style={styles.documentActions}>
          {item.verified_at ? (
            <View style={styles.verifiedBadge}>
              <Ionicons name="checkmark-circle" size={16} color="#2ECC71" />
              <Text style={styles.verifiedText}>Doğrulandı</Text>
            </View>
          ) : (
            <TouchableOpacity
              style={styles.verifyButton}
              onPress={() => handleVerifyDocument(item)}
            >
              <Ionicons name="checkmark" size={16} color="#0D3B66" />
              <Text style={styles.verifyButtonText}>Doğrula</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {item.verified_at && item.verifier && (
        <View style={styles.verificationInfo}>
          <Text style={styles.verificationText}>
            {item.verifier.full_name} tarafından {formatDate(item.verified_at)} tarihinde doğrulandı
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="small" color="#0D3B66" />
        <Text style={styles.loadingText}>Belgeler yükleniyor...</Text>
      </View>
    );
  }

  if (documents.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="document-outline" size={48} color="#8E9297" />
        <Text style={styles.emptyText}>Henüz belge eklenmemiş</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {documents.map((item) => (
        <View key={item.id}>
          {renderDocumentItem({ item })}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#8E9297',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#8E9297',
    marginTop: 12,
    textAlign: 'center',
  },
  documentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  documentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  documentInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  documentIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F4F8',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  documentDetails: {
    flex: 1,
  },
  documentType: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  documentDate: {
    fontSize: 12,
    color: '#8E9297',
    marginBottom: 2,
  },
  documentUploader: {
    fontSize: 12,
    color: '#8E9297',
  },
  documentActions: {
    alignItems: 'flex-end',
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  verifiedText: {
    fontSize: 12,
    color: '#2ECC71',
    marginLeft: 4,
    fontWeight: '500',
  },
  verifyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F4F8',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  verifyButtonText: {
    fontSize: 12,
    color: '#0D3B66',
    marginLeft: 4,
    fontWeight: '500',
  },
  verificationInfo: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#E1E8ED',
  },
  verificationText: {
    fontSize: 12,
    color: '#8E9297',
    fontStyle: 'italic',
  },
});

export default DocumentList;
