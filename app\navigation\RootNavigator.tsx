import React, { useState, useEffect } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { View, StyleSheet } from 'react-native';
import LoginScreen from '../screens/LoginScreen';
import FirstTimeLoginScreenNew from '../screens/FirstTimeLoginScreenNew';
import RoleBasedNavigator from './RoleBasedNavigator';
import { authService, User } from '../services/authService';
import { useAuthStore } from '../store/authStore';
import { useUserActivity } from '../hooks/useUserActivity';
import SessionTimeoutModal from '../components/SessionTimeoutModal';

const Stack = createStackNavigator();

export default function RootNavigator() {
  console.log('RootNavigator rendering...');

  // State management
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [showFirstTimeLogin, setShowFirstTimeLogin] = useState(false);

  // Auth store
  const showSessionWarning = useAuthStore((state) => state.showSessionWarning);
  const extendSession = useAuthStore((state) => state.extendSession);
  const logout = useAuthStore((state) => state.logout);

  // User activity tracking
  const { panResponder } = useUserActivity();

  useEffect(() => {
    const initializeAuth = async () => {
      // Auth service'i başlat (geçersiz session'ları temizle)
      await authService.initialize();

      // Auth state değişikliklerini dinle
      const { data: { subscription } } = authService.onAuthStateChange((user) => {
        console.log('Auth state changed:', user);
        setUser(user);
        setLoading(false);
      });

      // Cleanup function'ı return et
      return () => {
        subscription?.unsubscribe();
      };
    };

    let cleanup: (() => void) | undefined;

    initializeAuth().then((cleanupFn) => {
      cleanup = cleanupFn;
    });

    // Cleanup
    return () => {
      cleanup?.();
    };
  }, []);

  const handleLogout = async () => {
    console.log('Logout initiated');
    await logout(); // Use auth store logout instead
    setUser(null);
    setShowFirstTimeLogin(false);
  };

  const handleShowFirstTimeLogin = () => {
    setShowFirstTimeLogin(true);
  };

  const handleBackToLogin = () => {
    setShowFirstTimeLogin(false);
  };

  const handleLogin = (user: User) => {
    const loginTime = new Date().toISOString();
    console.log(`Login successful at ${loginTime}: ${JSON.stringify(user)}`);
    console.log('Calling onLogin function...');
    
    // AuthStore'u güncelle (setUser otomatik olarak session timeout'u başlatır)
    useAuthStore.getState().setUser(user);
    
    setUser(user);
    console.log('onLogin function called');
  };

  if (loading) {
    return <View style={styles.container} />;
  }

  return (
    <View style={styles.container} {...panResponder.panHandlers}>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {!user ? (
          <>
            {!showFirstTimeLogin ? (
              <Stack.Screen name="Login">
                {(props) => (
                  <LoginScreen
                    {...props}
                    onLogin={handleLogin}
                    onShowFirstTimeLogin={handleShowFirstTimeLogin}
                  />
                )}
              </Stack.Screen>
            ) : (
              <Stack.Screen name="FirstTimeLogin">
                {(props) => (
                  <FirstTimeLoginScreenNew
                    {...props}
                    onBack={handleBackToLogin}
                    onSuccess={() => {
                      setShowFirstTimeLogin(false);
                      // User will be set by auth state change
                    }}
                  />
                )}
              </Stack.Screen>
            )}
          </>
        ) : (
          <Stack.Screen name="Main">
            {(props) => <RoleBasedNavigator {...props} user={user} onLogout={handleLogout} />}
          </Stack.Screen>
        )}
      </Stack.Navigator>

      {/* Session Timeout Modal */}
      <SessionTimeoutModal
        visible={showSessionWarning}
        onExtendSession={extendSession}
        onLogout={handleLogout}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
});
