import { create } from 'zustand';
import { authService } from '../services/authService';
import { sessionTimeoutService } from '../services/sessionTimeoutService';
import { APP_CONFIG } from '../constants/config';
import { User } from '../services/authService';

interface AuthState {
  // State
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  showSessionWarning: boolean;

  // Actions
  login: (email: string, password: string) => Promise<boolean>;
  sendMagicLink: (email: string) => Promise<boolean>;
  logout: () => Promise<void>;
  clearError: () => void;
  setUser: (user: User | null) => void;
  initialize: () => Promise<void>;

  // Session timeout actions
  startSessionTimeout: () => void;
  stopSessionTimeout: () => void;
  extendSession: () => void;
  handleSessionWarning: () => void;
  handleSessionTimeout: () => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  // Initial state
  user: null,
  isLoading: false,
  isAuthenticated: false,
  error: null,
  showSessionWarning: false,

  // Login with email/password
  login: async (email: string, password: string) => {
    console.log('AuthStore: Login attempt for:', email);
    
    try {
      const result = await authService.signInWithPassword(email, password);
      
      if (result.user) {
        console.log('Login successful:', result.user);
        console.log(`Login successful at ${new Date().toISOString()}:`, result.user);
        
        set({ user: result.user, isAuthenticated: true, error: null });
        
        // Session timeout'u başlat
        console.log('AuthStore: Calling startSessionTimeout after successful login');
        get().startSessionTimeout();
        
        return true;
      } else {
        console.log('Login failed:', result.error);
        set({ error: result.error || 'Unknown error' });
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      set({ error: 'An unexpected error occurred' });
      return false;
    }
  },

  // Send magic link
  sendMagicLink: async (email: string) => {
    set({ isLoading: true, error: null });

    try {
      const response = await authService.signInWithMagicLink(email);

      if (response.success) {
        set({ isLoading: false, error: null });
        return true;
      } else {
        set({
          error: response.error || 'Magic link gönderilemedi',
          isLoading: false
        });
        return false;
      }
    } catch (error) {
      set({
        error: 'Magic link gönderilemedi',
        isLoading: false
      });
      return false;
    }
  },

  // Logout işlemi
  logout: async () => {
    try {
      console.log('AuthStore: Logging out user at', new Date().toISOString());
      set({ isLoading: true });
      
      // Önce session timeout'u durdur
      get().stopSessionTimeout();
      
      // Sonra auth service ile logout işlemini gerçekleştir
      await authService.logout();
      
      // State'i temizle
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        showSessionWarning: false,
      });
      
      console.log('AuthStore: User logged out successfully');
    } catch (error) {
      console.error('AuthStore: Error during logout:', error);
      set({
        isLoading: false,
        error: 'Çıkış yapılırken bir hata oluştu',
      });
    }
  },

  // Clear error
  clearError: () => {
    set({ error: null });
  },

  // Set user (for auth state changes)
  setUser: (user: User | null) => {
    const currentUser = get().user;
    set({
      user,
      isAuthenticated: !!user,
      isLoading: false
    });

    // Eğer kullanıcı giriş yaptıysa session timeout'u başlat
    if (user && !currentUser) {
      get().startSessionTimeout();
    }
    // Eğer kullanıcı çıkış yaptıysa session timeout'u durdur
    else if (!user && currentUser) {
      get().stopSessionTimeout();
    }
  },

  // Initialize auth state
  initialize: async () => {
    set({ isLoading: true });

    try {
      const user = await authService.getCurrentUser();
      set({
        user,
        isAuthenticated: !!user,
        isLoading: false
      });

      // Eğer kullanıcı varsa session timeout'u başlat
      if (user) {
        get().startSessionTimeout();
      }

      // Listen for auth state changes
      authService.onAuthStateChange((user) => {
        get().setUser(user);
      });
    } catch (error) {
      set({ isLoading: false });
    }
  },

  // Session timeout'u başlat
  startSessionTimeout: () => {
    console.log('AuthStore: 🚀 Session timeout başlatılıyor');
    
    if (APP_CONFIG.debug?.showSessionTimeoutLogs) {
      console.log(`AuthStore: DEBUG - Starting session timeout at ${new Date().toISOString()}`);
      
      const user = get().user;
      console.log(`AuthStore: Starting session timeout for user: ${user ? `${user.full_name} (${user.email})` : 'Unknown user'}`);
    }
    
    sessionTimeoutService.start({
      onWarning: () => {
        console.log('AuthStore: Session warning triggered');
        
        if (APP_CONFIG.debug?.showSessionTimeoutLogs) {
          console.log(`AuthStore: ⚠️ Session uyarısı tetiklendi: ${new Date().toISOString()}`);
        }
        
        get().handleSessionWarning();
      },
      onTimeout: () => {
        console.log('AuthStore: Session timeout triggered');
        
        if (APP_CONFIG.debug?.showSessionTimeoutLogs) {
          console.log(`AuthStore: ⏰ Session timeout tetiklendi: ${new Date().toISOString()}`);
        }
        
        get().handleSessionTimeout();
      },
    });
    
    console.log('AuthStore: ✅ Session timeout servisi başarıyla başlatıldı');
  },

  // Session timeout'u durdur
  stopSessionTimeout: () => {
    console.log('AuthStore: 🛑 Session timeout durduruluyor');
    sessionTimeoutService.stop();
    set({ showSessionWarning: false });
  },

  // Oturumu uzat
  extendSession: () => {
    console.log('AuthStore: ⏰ Session uzatılıyor');
    sessionTimeoutService.extendSession();
    set({ showSessionWarning: false });
  },

  // Session warning'i handle et
  handleSessionWarning: () => {
    const user = get().user;
    console.log(
      'AuthStore: ⚠️ Session uyarısı işleniyor - Kullanıcı:',
      user ? `${user.full_name} (${user.email})` : 'Bilinmeyen kullanıcı',
      'Zaman:',
      new Date().toISOString()
    );
    set({ showSessionWarning: true });
  },

  // Session timeout'u handle et
  handleSessionTimeout: () => {
    const user = get().user;
    console.log(
      'AuthStore: ⏰ Session timeout işleniyor - Kullanıcı:',
      user ? `${user.full_name} (${user.email})` : 'Bilinmeyen kullanıcı',
      'Zaman:',
      new Date().toISOString()
    );
    
    // Timeout durumunda kesin olarak çıkış yap
    console.log('AuthStore: 🚪 Session timeout nedeniyle zorla logout yapılıyor');
    
    // Önce timeout servisini durdur
    sessionTimeoutService.stop();
    
    // Sonra logout işlemini gerçekleştir
    get().logout();
  },
}));
