import { useEffect, useRef } from 'react';
import { PanResponder, Dimensions, AppState, TouchableWithoutFeedback } from 'react-native';
import { useAuthStore } from '../store/authStore';

export const useUserActivity = () => {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const lastActivityRef = useRef<number>(Date.now());

  // Throttle function to prevent too many calls
  const throttledRecordActivity = () => {
    const now = Date.now();
    // Only record activity if at least 2 seconds have passed since last activity
    if (now - lastActivityRef.current > 2000) {
      lastActivityRef.current = now;
      if (isAuthenticated) {
        console.log(`useUserActivity: Recording user activity at ${new Date(now).toISOString()}`);
        // recordUserActivity artık kullanılmıyor - yeni session timeout sistemi login zamanına dayalı
      }
    }
  };

  // PanResponder for touch events - optimize to reduce unnecessary calls
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => {
        throttledRecordActivity();
        return false; // Don't capture the gesture
      },
      onMoveShouldSetPanResponder: () => false, // Don't need to track every move
      onPanResponderGrant: () => {
        throttledRecordActivity();
      },
      // Reduce frequency of move events - only track release
      onPanResponderRelease: () => {
        throttledRecordActivity();
      },
    })
  ).current;

  // Navigation activity tracking
  useEffect(() => {
    if (isAuthenticated) {
      console.log(`useUserActivity: Initial activity recorded on authentication at ${new Date().toISOString()}`);
      throttledRecordActivity();
    }
  }, [isAuthenticated]);

  // Periodic activity check (her 10 saniyede bir)
  useEffect(() => {
    if (!isAuthenticated) return;
    
    console.log('useUserActivity: Setting up periodic activity check');
    
    const intervalId = setInterval(() => {
      console.log(`useUserActivity: Periodic activity check at ${new Date().toISOString()}`);
      throttledRecordActivity();
    }, 10000); // 10 saniyede bir (15 saniyeden düşürüldü)
    
    return () => {
      console.log('useUserActivity: Cleaning up periodic activity check');
      clearInterval(intervalId);
    };
  }, [isAuthenticated]);

  // AppState değişikliklerini izle
  useEffect(() => {
    if (!isAuthenticated) return;
    
    console.log('useUserActivity: Setting up AppState listener');
    
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      console.log(`useUserActivity: AppState changed to ${nextAppState} at ${new Date().toISOString()}`);
      if (nextAppState === 'active') {
        console.log(`useUserActivity: App came to foreground at ${new Date().toISOString()}, recording activity`);
        // Ön plana geldiğinde hemen aktivite kaydet
        throttledRecordActivity();
        
        // Ek olarak 1 saniye sonra tekrar kaydet (bazen ilk kayıt işlemi kaçabilir)
        setTimeout(() => {
          console.log(`useUserActivity: Follow-up activity record after foreground at ${new Date().toISOString()}`);
          throttledRecordActivity();
        }, 1000);
      }
    });
    
    return () => {
      console.log('useUserActivity: Cleaning up AppState listener');
      subscription.remove();
    };
  }, [isAuthenticated]);

  // Ekstra bir güvenlik olarak, her 30 saniyede bir aktivite kaydı yap
  useEffect(() => {
    if (!isAuthenticated) return;
    
    console.log('useUserActivity: Setting up backup activity recorder');
    
    const backupInterval = setInterval(() => {
      console.log(`useUserActivity: Backup activity record at ${new Date().toISOString()}`);
      // recordUserActivity artık kullanılmıyor - yeni session timeout sistemi login zamanına dayalı
    }, 30000); // 30 saniyede bir
    
    return () => {
      console.log('useUserActivity: Cleaning up backup activity recorder');
      clearInterval(backupInterval);
    };
  }, [isAuthenticated]);

  return {
    panResponder,
    recordActivity: throttledRecordActivity,
  };
};
