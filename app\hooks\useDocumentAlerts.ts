import { useTranslation } from 'react-i18next';
import { useAlertHelpers } from './useAlert';
import { Alert } from 'react-native';

interface DocumentAlertOptions {
  onTakePhoto?: () => void;
  onSelectFromGallery?: () => void;
  onCancel?: () => void;
}

interface DocumentRequestOptions {
  onAdditionalCopy?: () => void;
  onSpecialDocument?: () => void;
  onCancel?: () => void;
}

export const useDocumentAlerts = () => {
  const { t } = useTranslation();
  const { showInfo } = useAlertHelpers();

  const showDocumentUploadAlert = (role: 'driver' | 'family' | 'admin', options: DocumentAlertOptions = {}) => {
    console.log(`Document Upload Alert triggered - ${role.charAt(0).toUpperCase() + role.slice(1)} Role`);
    console.log('Alert Title:', t('alerts.documentUpload'));
    console.log('Alert Message:', t('alerts.documentUploadMessage'));
    console.log('Button 1:', t('common.cancel'));
    console.log('Button 2:', t('documents.takePhoto'));
    console.log('Button 3:', t('documents.selectFromGallery'));
    
    showInfo(
      t('alerts.documentUpload'),
      t('alerts.documentUploadMessage'),
      [
        { 
          text: t('common.cancel'), 
          style: 'cancel',
          onPress: options.onCancel
        },
        { 
          text: t('documents.takePhoto'), 
          onPress: options.onTakePhoto || (() => console.log('Camera opened'))
        },
        { 
          text: t('documents.selectFromGallery'), 
          onPress: options.onSelectFromGallery || (() => console.log('Gallery opened'))
        },
      ]
    );
  };

  const showDocumentRequestAlert = (role: 'family', options: DocumentRequestOptions = {}) => {
    console.log(`Document Request Alert triggered - ${role.charAt(0).toUpperCase() + role.slice(1)} Role`);
    console.log('Alert Title:', t('alerts.documentRequest'));
    console.log('Alert Message:', t('alerts.documentRequestMessage'));
    console.log('Button 1:', t('common.cancel'));
    console.log('Button 2:', t('documents.additionalCopy'));
    console.log('Button 3:', t('documents.specialDocument'));
    
    Alert.alert(
      t('alerts.documentRequest'),
      t('alerts.documentRequestMessage'),
      [
        { 
          text: t('common.cancel'), 
          style: 'cancel',
          onPress: options.onCancel
        },
        { 
          text: t('documents.additionalCopy'), 
          onPress: options.onAdditionalCopy || (() => console.log('Requesting additional copy'))
        },
        { 
          text: t('documents.specialDocument'), 
          onPress: options.onSpecialDocument || (() => console.log('Requesting special document'))
        },
      ]
    );
  };

  return {
    showDocumentUploadAlert,
    showDocumentRequestAlert,
  };
};