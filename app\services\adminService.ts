import { supabase } from '../lib/supabase';
import { authService } from './authService';

export interface UserMember {
  id: string;
  email: string;
  full_name: string;
  role: 'ADMIN' | 'DRIVER' | 'FAMILY';
  phone?: string;
  status: 'ACTIVE' | 'ON_LEAVE' | 'INACTIVE';
  created_at: string;
  has_password?: boolean; // Auth.users'da var mı?
}

export interface AdminResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  errorCode?: string;
}

export interface CreateUserData {
  email: string;
  full_name: string;
  role: 'ADMIN' | 'DRIVER' | 'FAMILY';
  phone?: string;
  location?: string;
  status?: 'ACTIVE' | 'ON_LEAVE' | 'INACTIVE';
}

class AdminService {
  // User listesini getir
  async getUserList(): Promise<AdminResponse<UserMember[]>> {
    try {
      console.log('AdminService: Getting user list...');

      const { data: users, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('AdminService: User list error:', error);
        return {
          success: false,
          error: 'Kullanıcı listesi alınamadı',
        };
      }

      // Her user için auth.users'da var mı kontrol et
      const usersWithPasswordStatus = await Promise.all(
        users.map(async (member) => {
          try {
            // Auth.users'da var mı kontrol et (admin RPC ile)
            const { data: authCheck } = await supabase.rpc('check_user_exists', {
              p_email: member.email
            });

            return {
              ...member,
              has_password: authCheck || false,
            };
          } catch (error) {
            console.log(`AdminService: Auth check failed for ${member.email}:`, error);
            return {
              ...member,
              has_password: false,
            };
          }
        })
      );

      return {
        success: true,
        data: usersWithPasswordStatus,
      };
    } catch (error) {
      console.error('AdminService: Get user list unexpected error:', error);
      return {
        success: false,
        error: 'Beklenmeyen bir hata oluştu',
      };
    }
  }

  // Yeni user oluştur
  async createUser(userData: CreateUserData): Promise<AdminResponse<UserMember>> {
    try {
      console.log('AdminService: Creating user:', userData);

      // 1. Users tablosuna ekle (UUID otomatik generate edilecek)
      const { data: newUser, error: userError } = await supabase
        .from('users')
        .insert({
          email: userData.email.trim().toLowerCase(),
          full_name: userData.full_name.trim(),
          role: userData.role,
          phone: userData.phone?.trim() || null,
          location: userData.location?.trim() || null,
          status: userData.status || 'ACTIVE',
        })
        .select()
        .single();

      if (userError) {
        console.error('AdminService: User creation error:', userError);

        if (userError.code === '23505') { // Unique constraint violation
          return {
            success: false,
            error: 'Bu e-posta adresi zaten kayıtlı',
            errorCode: '23505',
          };
        }

        if (userError.code === '23502') { // Not null constraint violation
          return {
            success: false,
            error: 'Veritabanı yapılandırma hatası. Lütfen yöneticinizle iletişime geçin.',
          };
        }

        // Diğer veritabanı hataları için genel mesaj
        return {
          success: false,
          error: 'Sürücü eklenirken bir hata oluştu. Lütfen tekrar deneyin.',
        };
      }



      return {
        success: true,
        data: { ...newUser, has_password: false },
        message: 'Kullanıcı başarıyla oluşturuldu',
      };
    } catch (error) {
      console.error('AdminService: Create user unexpected error:', error);
      return {
        success: false,
        error: 'Beklenmeyen bir hata oluştu',
      };
    }
  }

  // User güncelle
  async updateUser(userId: string, updateData: Partial<CreateUserData>): Promise<AdminResponse<UserMember>> {
    try {
      console.log('AdminService: Updating user:', userId, updateData);

      const { data: updatedUser, error } = await supabase
        .from('users')
        .update({
          full_name: updateData.full_name?.trim(),
          role: updateData.role,
          phone: updateData.phone?.trim() || null,
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        console.error('AdminService: User update error:', error);
        return {
          success: false,
          error: 'Kullanıcı güncellenemedi',
        };
      }

      return {
        success: true,
        data: updatedUser,
        message: 'Kullanıcı başarıyla güncellendi',
      };
    } catch (error) {
      console.error('AdminService: Update user unexpected error:', error);
      return {
        success: false,
        error: 'Beklenmeyen bir hata oluştu',
      };
    }
  }

  // User sil (soft delete)
  async deleteUser(userId: string): Promise<AdminResponse> {
    try {
      console.log('AdminService: Deleting user:', userId);

      const { error } = await supabase
        .from('users')
        .update({
          status: 'INACTIVE',
        })
        .eq('id', userId);

      if (error) {
        console.error('AdminService: User delete error:', error);
        return {
          success: false,
          error: 'Kullanıcı silinemedi',
        };
      }

      return {
        success: true,
        message: 'Kullanıcı başarıyla silindi',
      };
    } catch (error) {
      console.error('AdminService: Delete user unexpected error:', error);
      return {
        success: false,
        error: 'Beklenmeyen bir hata oluştu',
      };
    }
  }


}

export const adminService = new AdminService();
