import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User } from '../../services/authService';
import { useTranslation } from 'react-i18next';
import { emailService } from '../../services/emailService';

interface FamilyCommunicationScreenProps {
  user: User;
  onBack: () => void;
}

const FamilyCommunicationScreen = ({ user, onBack }: FamilyCommunicationScreenProps) => {
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState<'messages' | 'contacts'>('messages');
  const [messageText, setMessageText] = useState('');

  // Mock data - gerçek uygulamada API'den gelecek
  const messages = [
    {
      id: '1',
      sender: '<PERSON>',
      sender_role: 'DRIVER',
      message: '<PERSON><PERSON><PERSON><PERSON>, belge teslimi tama<PERSON>. Nüfus müdürlüğünden onay aldık.',
      timestamp: '2024-01-20T14:30:00Z',
      is_from_family: false,
      is_read: true,
    },
    {
      id: '2',
      sender: 'Mehmet Yılmaz',
      sender_role: 'FAMILY',
      message: 'Teşekkür ederim. Cenaze töreni için ne zaman bilgi alacağız?',
      timestamp: '2024-01-20T14:35:00Z',
      is_from_family: true,
      is_read: true,
    },
    {
      id: '3',
      sender: 'Ali Kaya',
      sender_role: 'DRIVER',
      message: 'Cami ile görüştüm. Yarın saat 14:00 için randevu aldık. Size detayları göndereceğim.',
      timestamp: '2024-01-20T15:00:00Z',
      is_from_family: false,
      is_read: true,
    },
    {
      id: '4',
      sender: 'Sistem',
      sender_role: 'SYSTEM',
      message: 'Cenaze töreni detayları güncellendi. Fatih Camii - 21.01.2024 Saat: 14:00',
      timestamp: '2024-01-20T15:15:00Z',
      is_from_family: false,
      is_read: false,
    },
  ];

  // İletişim listesi - email servisinden alınıyor
  const contacts = emailService.getContacts().map((contact, index) => ({
    ...contact,
    status: index < 2 ? 'online' : 'offline', // İlk 2 kişi online
    avatar_color: ['#2ECC71', '#9B59B6', '#0D3B66', '#F39C12'][index] || '#34495E',
  }));

  const handleSendMessage = () => {
    if (!messageText.trim()) {
      Alert.alert(t('common.warning'), t('alerts.writeMessagePlease'));
      return;
    }

    Alert.alert(
      'Mesaj Gönder',
      `Mesajınız gönderilsin mi?\n\n"${messageText}"`,
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Gönder', 
          onPress: () => {
            console.log('Message sent:', messageText);
            setMessageText('');
          }
        },
      ]
    );
  };

  const handleCallContact = (name: string, phone: string) => {
    Alert.alert(
      'Arama',
      `${name} ile iletişime geçmek istediğinizden emin misiniz?\n\n${phone}`,
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Ara', onPress: () => console.log(`Calling ${phone}`) },
      ]
    );
  };

  const handleEmailContact = (name: string, email: string) => {
    Alert.alert(
      'E-posta',
      `${name} adresine e-posta göndermek istediğinizden emin misiniz?\n\n${email}`,
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'E-posta Gönder', onPress: () => console.log(`Emailing ${email}`) },
      ]
    );
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
    });
  };

  const getSenderInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getSenderColor = (role: string) => {
    switch (role) {
      case 'DRIVER': return '#2ECC71';
      case 'FAMILY': return '#9B59B6';
      case 'SYSTEM': return '#0D3B66';
      default: return '#8E9297';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#9B59B6" />
        </TouchableOpacity>
        <Text style={styles.title}>İletişim</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Tabs */}
      <View style={styles.tabSection}>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'messages' && styles.activeTab]}
          onPress={() => setSelectedTab('messages')}
        >
          <Ionicons 
            name="chatbubbles" 
            size={20} 
            color={selectedTab === 'messages' ? '#9B59B6' : '#8E9297'} 
          />
          <Text style={[styles.tabText, selectedTab === 'messages' && styles.activeTabText]}>
            Mesajlar
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'contacts' && styles.activeTab]}
          onPress={() => setSelectedTab('contacts')}
        >
          <Ionicons 
            name="people" 
            size={20} 
            color={selectedTab === 'contacts' ? '#9B59B6' : '#8E9297'} 
          />
          <Text style={[styles.tabText, selectedTab === 'contacts' && styles.activeTabText]}>
            Kişiler
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <KeyboardAvoidingView 
        style={styles.content} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {selectedTab === 'messages' ? (
          <>
            <ScrollView style={styles.messagesContainer} showsVerticalScrollIndicator={false}>
              {messages.map((message) => (
                <View
                  key={message.id}
                  style={[
                    styles.messageCard,
                    message.is_from_family ? styles.sentMessage : styles.receivedMessage
                  ]}
                >
                  {!message.is_from_family && (
                    <View style={styles.messageHeader}>
                      <View style={[
                        styles.senderAvatar,
                        { backgroundColor: getSenderColor(message.sender_role) }
                      ]}>
                        <Text style={styles.senderInitials}>
                          {getSenderInitials(message.sender)}
                        </Text>
                      </View>
                      <View style={styles.senderInfo}>
                        <Text style={styles.senderName}>{message.sender}</Text>
                        <Text style={styles.senderRole}>
                          {message.sender_role === 'DRIVER' ? 'Sürücü' :
                           message.sender_role === 'SYSTEM' ? 'Sistem' : 'Diğer'}
                        </Text>
                      </View>
                      <Text style={styles.messageTime}>
                        {formatTime(message.timestamp)}
                      </Text>
                    </View>
                  )}
                  
                  <Text style={[
                    styles.messageText,
                    message.is_from_family ? styles.sentMessageText : styles.receivedMessageText
                  ]}>
                    {message.message}
                  </Text>
                  
                  {message.is_from_family && (
                    <View style={styles.sentMessageFooter}>
                      <Text style={styles.sentMessageTime}>
                        {formatTime(message.timestamp)}
                      </Text>
                      <Ionicons 
                        name={message.is_read ? 'checkmark-done' : 'checkmark'} 
                        size={16} 
                        color={message.is_read ? '#2ECC71' : '#8E9297'} 
                      />
                    </View>
                  )}
                </View>
              ))}
            </ScrollView>

            {/* Message Input */}
            <View style={styles.messageInputContainer}>
              <TextInput
                style={styles.messageInput}
                placeholder="Mesajınızı yazın..."
                placeholderTextColor="#8E9297"
                value={messageText}
                onChangeText={setMessageText}
                multiline
                maxLength={500}
              />
              <TouchableOpacity
                style={[
                  styles.sendButton,
                  messageText.trim() ? styles.sendButtonActive : styles.sendButtonInactive
                ]}
                onPress={handleSendMessage}
                disabled={!messageText.trim()}
              >
                <Ionicons 
                  name="send" 
                  size={20} 
                  color={messageText.trim() ? '#FFFFFF' : '#8E9297'} 
                />
              </TouchableOpacity>
            </View>
          </>
        ) : (
          <ScrollView style={styles.contactsContainer} showsVerticalScrollIndicator={false}>
            {contacts.map((contact) => (
              <View key={contact.id} style={styles.contactCard}>
                <View style={styles.contactHeader}>
                  <View style={[styles.contactAvatar, { backgroundColor: contact.avatar_color }]}>
                    <Text style={styles.contactInitials}>
                      {getSenderInitials(contact.name)}
                    </Text>
                    <View style={[
                      styles.statusIndicator,
                      { backgroundColor: contact.status === 'online' ? '#2ECC71' : '#E1E8ED' }
                    ]} />
                  </View>
                  <View style={styles.contactInfo}>
                    <Text style={styles.contactName}>{contact.name}</Text>
                    <Text style={styles.contactRole}>{contact.role}</Text>
                    <Text style={styles.contactStatus}>
                      {contact.status === 'online' ? 'Çevrimiçi' : 'Çevrimdışı'}
                    </Text>
                  </View>
                </View>

                <View style={styles.contactActions}>
                  <TouchableOpacity
                    style={styles.contactActionButton}
                    onPress={() => handleCallContact(contact.name, contact.phone)}
                  >
                    <Ionicons name="call" size={20} color="#2ECC71" />
                    <Text style={styles.contactActionText}>Ara</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={styles.contactActionButton}
                    onPress={() => handleEmailContact(contact.name, contact.email)}
                  >
                    <Ionicons name="mail" size={20} color="#9B59B6" />
                    <Text style={styles.contactActionText}>E-posta</Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.contactDetails}>
                  <View style={styles.contactDetailRow}>
                    <Ionicons name="call" size={14} color="#8E9297" />
                    <Text style={styles.contactDetailText}>{contact.phone}</Text>
                  </View>
                  <View style={styles.contactDetailRow}>
                    <Ionicons name="mail" size={14} color="#8E9297" />
                    <Text style={styles.contactDetailText}>{contact.email}</Text>
                  </View>
                </View>
              </View>
            ))}

            {/* Emergency Info */}
            <View style={styles.emergencyCard}>
              <View style={styles.emergencyHeader}>
                <Ionicons name="warning" size={24} color="#E74C3C" />
                <Text style={styles.emergencyTitle}>Acil Durum</Text>
              </View>
              <Text style={styles.emergencyText}>
                Acil durumlar için 7/24 destek hattımızı arayabilirsiniz.
              </Text>
              <TouchableOpacity
                style={styles.emergencyButton}
                onPress={() => handleCallContact('DITIB Destek', '+90 212 XXX XX XX')}
              >
                <Ionicons name="call" size={20} color="#FFFFFF" />
                <Text style={styles.emergencyButtonText}>ACİL ARAMA</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  placeholder: {
    width: 40,
  },
  tabSection: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#9B59B6',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#8E9297',
    marginLeft: 8,
  },
  activeTabText: {
    color: '#9B59B6',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  messageCard: {
    marginBottom: 16,
    borderRadius: 16,
    padding: 16,
  },
  receivedMessage: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E1E8ED',
    alignSelf: 'flex-start',
    maxWidth: '85%',
  },
  sentMessage: {
    backgroundColor: '#9B59B6',
    alignSelf: 'flex-end',
    maxWidth: '85%',
  },
  messageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  senderAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  senderInitials: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  senderInfo: {
    flex: 1,
  },
  senderName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  senderRole: {
    fontSize: 12,
    color: '#8E9297',
  },
  messageTime: {
    fontSize: 12,
    color: '#8E9297',
  },
  messageText: {
    fontSize: 14,
    lineHeight: 20,
  },
  receivedMessageText: {
    color: '#1C1C1E',
  },
  sentMessageText: {
    color: '#FFFFFF',
  },
  sentMessageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 8,
  },
  sentMessageTime: {
    fontSize: 12,
    color: '#E1C4F0',
    marginRight: 4,
  },
  messageInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E1E8ED',
  },
  messageInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E1E8ED',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    maxHeight: 100,
    marginRight: 12,
  },
  sendButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonActive: {
    backgroundColor: '#9B59B6',
  },
  sendButtonInactive: {
    backgroundColor: '#F5F8FA',
  },
  contactsContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  contactCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  contactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  contactAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    position: 'relative',
  },
  contactInitials: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    position: 'absolute',
    bottom: 0,
    right: 0,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  contactRole: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 2,
  },
  contactStatus: {
    fontSize: 12,
    color: '#2ECC71',
  },
  contactActions: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  contactActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F8FA',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
  },
  contactActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
    marginLeft: 8,
  },
  contactDetails: {
    borderTopWidth: 1,
    borderTopColor: '#F5F8FA',
    paddingTop: 16,
  },
  contactDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  contactDetailText: {
    fontSize: 14,
    color: '#8E9297',
    marginLeft: 8,
  },
  emergencyCard: {
    backgroundColor: '#FFF5F5',
    borderRadius: 16,
    padding: 20,
    marginBottom: 32,
    borderWidth: 1,
    borderColor: '#E74C3C',
  },
  emergencyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  emergencyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E74C3C',
    marginLeft: 8,
  },
  emergencyText: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 16,
    lineHeight: 20,
  },
  emergencyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E74C3C',
    paddingVertical: 12,
    borderRadius: 8,
  },
  emergencyButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 8,
  },
});

export default FamilyCommunicationScreen;
