import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Switch,
  TouchableOpacity,
  Alert,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTranslation } from 'react-i18next';

interface NotificationsScreenProps {
  onBack: () => void;
}

const NotificationsScreen = ({ onBack }: NotificationsScreenProps) => {
  const { t } = useTranslation();
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [vibrationEnabled, setVibrationEnabled] = useState(true);
  const [loading, setLoading] = useState(false);

  // Ayarları yükle
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const soundSetting = await AsyncStorage.getItem('notification_sound');
      const vibrationSetting = await AsyncStorage.getItem('notification_vibration');

      if (soundSetting !== null) {
        setSoundEnabled(JSON.parse(soundSetting));
      }
      if (vibrationSetting !== null) {
        setVibrationEnabled(JSON.parse(vibrationSetting));
      }
    } catch (error) {
      console.log('Settings load error:', error);
    }
  };

  const saveSettings = async () => {
    try {
      setLoading(true);
      await AsyncStorage.setItem('notification_sound', JSON.stringify(soundEnabled));
      await AsyncStorage.setItem('notification_vibration', JSON.stringify(vibrationEnabled));

      Alert.alert(t('common.success'), t('notifications.settingsSaved'));
    } catch (error) {
      Alert.alert(t('common.error'), t('notifications.settingsNotSaved'));
    } finally {
      setLoading(false);
    }
  };

  const handleSoundToggle = (value: boolean) => {
    setSoundEnabled(value);
  };

  const handleVibrationToggle = (value: boolean) => {
    setVibrationEnabled(value);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#0D3B66" />
        </TouchableOpacity>
        <Text style={styles.title}>{t('notifications.title')}</Text>
      </View>

      {/* Content */}
      <View style={styles.content}>
        <Text style={styles.description}>
          {t('notifications.description')}
        </Text>

        {/* Sound Setting */}
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Ionicons name="volume-high" size={24} color="#0D3B66" />
            <View style={styles.settingText}>
              <Text style={styles.settingTitle}>{t('notifications.sound')}</Text>
              <Text style={styles.settingSubtitle}>
                {t('notifications.soundDescription')}
              </Text>
            </View>
          </View>
          <Switch
            value={soundEnabled}
            onValueChange={handleSoundToggle}
            trackColor={{ false: '#E1E8ED', true: '#0D3B66' }}
            thumbColor={soundEnabled ? '#FFFFFF' : '#8E9297'}
          />
        </View>

        {/* Vibration Setting */}
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Ionicons name="phone-portrait" size={24} color="#0D3B66" />
            <View style={styles.settingText}>
              <Text style={styles.settingTitle}>{t('notifications.vibration')}</Text>
              <Text style={styles.settingSubtitle}>
                {t('notifications.vibrationDescription')}
              </Text>
            </View>
          </View>
          <Switch
            value={vibrationEnabled}
            onValueChange={handleVibrationToggle}
            trackColor={{ false: '#E1E8ED', true: '#0D3B66' }}
            thumbColor={vibrationEnabled ? '#FFFFFF' : '#8E9297'}
          />
        </View>

        {/* Info Box */}
        <View style={styles.infoBox}>
          <Ionicons name="information-circle" size={20} color="#FAA916" />
          <Text style={styles.infoText}>
            {t('notifications.infoMessage')}
          </Text>
        </View>

        {/* Save Button */}
        <TouchableOpacity
          style={[styles.saveButton, loading && styles.saveButtonDisabled]}
          onPress={saveSettings}
          disabled={loading}
        >
          <Text style={styles.saveButtonText}>
            {loading ? t('common.saving') : t('notifications.saveSettings')}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 24,
  },
  description: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 32,
    lineHeight: 20,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: 16,
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#8E9297',
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: '#FFF9E6',
    padding: 16,
    borderRadius: 12,
    marginTop: 24,
    marginBottom: 32,
    borderWidth: 1,
    borderColor: '#FAA916',
  },
  infoText: {
    fontSize: 14,
    color: '#8B6914',
    marginLeft: 12,
    flex: 1,
    lineHeight: 20,
  },
  saveButton: {
    backgroundColor: '#0D3B66',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#8E9297',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default NotificationsScreen;
