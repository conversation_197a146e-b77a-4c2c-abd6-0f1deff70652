import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DocumentUploadModal from '../components/DocumentUploadModal';
import DocumentList from '../components/DocumentList';
import ScreenLayout from '../components/ScreenLayout';
import { useTranslation } from 'react-i18next';

interface CaseItem {
  id: string;
  status: string;
  burial_type: string;
  family_contact_name: string;
  family_phone: string;
  family_email: string;
  created_at: string;
  deceased: {
    id: string;
    full_name: string;
    date_of_death: string;
    nationality: string;
  } | null;
}

interface CaseDetailScreenProps {
  route?: any;
  navigation?: any;
}

const CaseDetailScreen = ({ route, navigation }: CaseDetailScreenProps) => {
  const { t } = useTranslation();
  const caseItem = route?.params?.caseItem;
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [documentRefreshTrigger, setDocumentRefreshTrigger] = useState(0);

  const onBack = () => {
    navigation?.goBack();
  };

  const handleUploadSuccess = () => {
    setDocumentRefreshTrigger(prev => prev + 1);
  };

  if (!caseItem) {
    return (
      <ScreenLayout
        title={t('cases.caseDetails')}
        showBackButton
        onBack={onBack}
      >
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Vaka bilgisi bulunamadı</Text>
        </View>
      </ScreenLayout>
    );
  }
  const timelineItems = [
    {
      id: '1',
      title: 'Vefat Bildirimi',
      description: 'Vefat bildirimi alındı',
      time: '2024-01-15 09:00',
      status: 'completed',
    },
    {
      id: '2',
      title: 'Belge Toplama',
      description: 'Gerekli belgeler toplanıyor',
      time: '2024-01-15 10:30',
      status: 'in-progress',
    },
    {
      id: '3',
      title: 'Nakil Hazırlığı',
      description: 'Nakil işlemleri başlatılacak',
      time: 'Beklemede',
      status: 'pending',
    },
    {
      id: '4',
      title: 'Defin İşlemleri',
      description: 'Defin işlemleri tamamlanacak',
      time: 'Beklemede',
      status: 'pending',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return '#2ECC71';
      case 'in-progress':
        return '#FAA916';
      default:
        return '#E1E8ED';
    }
  };

  const renderTimelineItem = (item: any, index: number) => (
    <View key={item.id} style={styles.timelineItem}>
      <View style={styles.timelineLeft}>
        <View
          style={[
            styles.timelineCircle,
            { backgroundColor: getStatusColor(item.status) }
          ]}
        />
        {index < timelineItems.length - 1 && (
          <View style={styles.timelineLine} />
        )}
      </View>
      <View style={styles.timelineContent}>
        <Text style={styles.timelineTitle}>{item.title}</Text>
        <Text style={styles.timelineDescription}>{item.description}</Text>
        <Text style={styles.timelineTime}>{item.time}</Text>
      </View>
    </View>
  );

  return (
    <ScreenLayout
      title={t('cases.caseDetails')}
      showBackButton
      onBack={onBack}
      scrollable
    >
        {/* Case Info Card */}
        <View style={styles.infoCard}>
          <Text style={styles.deceasedName}>{caseItem.deceased?.full_name || t('common.unknown')}</Text>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Durum:</Text>
            <View style={[styles.statusBadge, { backgroundColor: caseItem.status === 'OPEN' ? '#E3F2FD' : '#E8F5E8' }]}>
              <Text style={[styles.statusText, { color: caseItem.status === 'OPEN' ? '#2196F3' : '#2ECC71' }]}>
                {caseItem.status === 'OPEN' ? 'Açık' : caseItem.status === 'CLOSED' ? 'Kapalı' : 'İptal Edildi'}
              </Text>
            </View>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Tarih:</Text>
            <Text style={styles.infoValue}>
              {caseItem.deceased?.date_of_death
                ? new Date(caseItem.deceased.date_of_death).toLocaleDateString('tr-TR')
                : new Date(caseItem.created_at).toLocaleDateString('tr-TR')
              }
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Defin Yeri:</Text>
            <Text style={styles.infoValue}>
              {caseItem.burial_type === 'DE' ? '🇩🇪 Almanya' : caseItem.burial_type === 'TR' ? '🇹🇷 Türkiye' : 'Belirtilmemiş'}
            </Text>
          </View>
          {caseItem.family_contact_name && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Aile İletişim:</Text>
              <Text style={styles.infoValue}>{caseItem.family_contact_name}</Text>
            </View>
          )}
          {caseItem.family_phone && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Telefon:</Text>
              <Text style={styles.infoValue}>{caseItem.family_phone}</Text>
            </View>
          )}
        </View>

        {/* Timeline */}
        <View style={styles.timelineCard}>
          <Text style={styles.sectionTitle}>İşlem Zaman Çizelgesi</Text>
          <View style={styles.timeline}>
            {timelineItems.map((item, index) => renderTimelineItem(item, index))}
          </View>
        </View>

        {/* Documents */}
        <View style={styles.documentsCard}>
          <View style={styles.documentsSectionHeader}>
            <Text style={styles.sectionTitle}>Belgeler</Text>
            <TouchableOpacity
              style={styles.addDocumentButton}
              onPress={() => setUploadModalVisible(true)}
            >
              <Ionicons name="add" size={16} color="#FFFFFF" />
              <Text style={styles.addDocumentText}>Belge Ekle</Text>
            </TouchableOpacity>
          </View>

          <DocumentList
            caseId={caseItem.id}
            currentUserId="current-user-id" // TODO: Get from auth context
            refreshTrigger={documentRefreshTrigger}
          />
        </View>

      {/* Document Upload Modal */}
      <DocumentUploadModal
        visible={uploadModalVisible}
        onClose={() => setUploadModalVisible(false)}
        caseId={caseItem.id}
        uploadedBy="current-user-id" // TODO: Get from auth context
        onUploadSuccess={handleUploadSuccess}
      />
    </ScreenLayout>
  );
};

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  deceasedName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    color: '#8E9297',
  },
  infoValue: {
    fontSize: 14,
    color: '#1C1C1E',
    fontWeight: '500',
  },
  statusBadge: {
    backgroundColor: '#F8D7DA',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    color: '#E74C3C',
    fontWeight: '500',
  },
  timelineCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 16,
  },
  timeline: {
    paddingLeft: 8,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  timelineLeft: {
    alignItems: 'center',
    marginRight: 16,
  },
  timelineCircle: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  timelineLine: {
    width: 2,
    height: 40,
    backgroundColor: '#E1E8ED',
    marginTop: 4,
  },
  timelineContent: {
    flex: 1,
  },
  timelineTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  timelineDescription: {
    fontSize: 12,
    color: '#8E9297',
    marginBottom: 4,
  },
  timelineTime: {
    fontSize: 12,
    color: '#8E9297',
  },
  documentsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
    marginBottom: 20,
  },
  documentsSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  addDocumentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0D3B66',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    gap: 4,
  },
  addDocumentText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  errorText: {
    fontSize: 16,
    color: '#E74C3C',
    textAlign: 'center',
    marginTop: 50,
  },
});

export default CaseDetailScreen;
