{"logs": [{"outputFile": "com.ditib.funeralapp-mergeDebugResources-36:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dbbbc453ac84f8e1f55ee9b7498bc2\\transformed\\appcompat-1.6.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,448,555,671,758,867,990,1069,1147,1238,1331,1426,1520,1620,1713,1808,1902,1993,2084,2169,2284,2393,2492,2618,2725,2833,2993,10055", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "443,550,666,753,862,985,1064,1142,1233,1326,1421,1515,1615,1708,1803,1897,1988,2079,2164,2279,2388,2487,2613,2720,2828,2988,3091,10136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\97dafb02ce1c76f135d92386cd4ea378\\transformed\\core-1.12.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "39,40,41,42,43,44,45,126", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3621,3724,3828,3931,4033,4138,4244,10794", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "3719,3823,3926,4028,4133,4239,4358,10890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\284bdced76504d9ec0db1a02932d1b74\\transformed\\material-1.9.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,386,485,561,652,736,842,971,1056,1121,1211,1286,1345,1436,1499,1564,1623,1694,1756,1813,1932,1990,2051,2106,2179,2311,2402,2491,2632,2710,2787,2879,2937,2988,3054,3126,3208,3290,3365,3439,3511,3590,3687,3768,3854,3946,4020,4099,4185,4239,4307,4390,4471,4533,4597,4660,4772,4875,4979,5084,5145,5200", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,100,98,75,90,83,105,128,84,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,88,140,77,76,91,57,50,65,71,81,81,74,73,71,78,96,80,85,91,73,78,85,53,67,82,80,61,63,62,111,102,103,104,60,54,81", "endOffsets": "280,381,480,556,647,731,837,966,1051,1116,1206,1281,1340,1431,1494,1559,1618,1689,1751,1808,1927,1985,2046,2101,2174,2306,2397,2486,2627,2705,2782,2874,2932,2983,3049,3121,3203,3285,3360,3434,3506,3585,3682,3763,3849,3941,4015,4094,4180,4234,4302,4385,4466,4528,4592,4655,4767,4870,4974,5079,5140,5195,5277"}, "to": {"startLines": "2,34,35,36,37,38,48,49,50,52,53,55,58,60,61,62,63,64,65,66,67,68,69,70,71,72,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3170,3271,3370,3446,3537,4567,4673,4802,4966,5031,5195,5425,5553,5644,5707,5772,5831,5902,5964,6021,6140,6198,6259,6314,6387,6750,6841,6930,7071,7149,7226,7318,7376,7427,7493,7565,7647,7729,7804,7878,7950,8029,8126,8207,8293,8385,8459,8538,8624,8678,8746,8829,8910,8972,9036,9099,9211,9314,9418,9523,9584,9639", "endLines": "5,34,35,36,37,38,48,49,50,52,53,55,58,60,61,62,63,64,65,66,67,68,69,70,71,72,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112", "endColumns": "12,100,98,75,90,83,105,128,84,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,88,140,77,76,91,57,50,65,71,81,81,74,73,71,78,96,80,85,91,73,78,85,53,67,82,80,61,63,62,111,102,103,104,60,54,81", "endOffsets": "330,3266,3365,3441,3532,3616,4668,4797,4882,5026,5116,5265,5479,5639,5702,5767,5826,5897,5959,6016,6135,6193,6254,6309,6382,6514,6836,6925,7066,7144,7221,7313,7371,7422,7488,7560,7642,7724,7799,7873,7945,8024,8121,8202,8288,8380,8454,8533,8619,8673,8741,8824,8905,8967,9031,9094,9206,9309,9413,9518,9579,9634,9716"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\043814979a7351adeafae334529a11be\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,239,333,412,486,557,641,710,778,857,941,1031,1113,1183,1275,1358,1440,1532,1616,1699,1771,1843,1928,2004,2081,2160", "endColumns": "73,109,93,78,73,70,83,68,67,78,83,89,81,69,91,82,81,91,83,82,71,71,84,75,76,78,79", "endOffsets": "124,234,328,407,481,552,636,705,773,852,936,1026,1108,1178,1270,1353,1435,1527,1611,1694,1766,1838,1923,1999,2076,2155,2235"}, "to": {"startLines": "33,46,47,51,54,56,57,59,73,74,75,113,114,115,116,118,119,120,121,122,123,124,125,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3096,4363,4473,4887,5121,5270,5341,5484,6519,6587,6666,9721,9811,9893,9963,10141,10224,10306,10398,10482,10565,10637,10709,10895,10971,11048,11127", "endColumns": "73,109,93,78,73,70,83,68,67,78,83,89,81,69,91,82,81,91,83,82,71,71,84,75,76,78,79", "endOffsets": "3165,4468,4562,4961,5190,5336,5420,5548,6582,6661,6745,9806,9888,9958,10050,10219,10301,10393,10477,10560,10632,10704,10789,10966,11043,11122,11202"}}]}]}