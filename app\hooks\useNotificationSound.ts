import { useEffect, useState } from 'react';
import { Audio } from 'expo-av';
import { Haptics } from 'expo-haptics';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const useNotificationSound = () => {
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [vibrationEnabled, setVibrationEnabled] = useState(true);
  const [sound, setSound] = useState<Audio.Sound | null>(null);

  // Ses ve titreşim ayarlarını yükle
  useEffect(() => {
    loadSettings();
    loadSound();
    
    return () => {
      // Cleanup
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, []);

  const loadSettings = async () => {
    try {
      const soundSetting = await AsyncStorage.getItem('notification_sound');
      const vibrationSetting = await AsyncStorage.getItem('notification_vibration');
      
      if (soundSetting !== null) {
        setSoundEnabled(JSON.parse(soundSetting));
      }
      if (vibrationSetting !== null) {
        setVibrationEnabled(JSON.parse(vibrationSetting));
      }
    } catch (error) {
      console.log('Settings load error:', error);
    }
  };

  const loadSound = async () => {
    try {
      // Expo'nun varsayılan bildirim sesini kullan
      const { sound: audioSound } = await Audio.Sound.createAsync(
        { uri: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav' },
        { shouldPlay: false }
      );
      setSound(audioSound);
      console.log('Notification sound loaded successfully');
    } catch (error) {
      console.log('Sound load error:', error);
      // Ses yüklenemezse sadece titreşim kullanılacak
    }
  };

  const playNotificationSound = async () => {
    // Ses çal
    if (soundEnabled) {
      try {
        if (sound) {
          await sound.replayAsync();
          console.log('Notification sound played');
        } else {
          console.log('Sound not loaded');
        }
      } catch (error) {
        console.log('Sound play error:', error);
      }
    } else {
      console.log('Notification sound is disabled');
    }

    // Titreşim uygula
    if (vibrationEnabled) {
      try {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
        console.log('Notification vibration triggered');
      } catch (error) {
        console.log('Vibration error:', error);
      }
    } else {
      console.log('Notification vibration is disabled');
    }
  };

  return {
    soundEnabled,
    vibrationEnabled,
    playNotificationSound,
    setSoundEnabled,
    setVibrationEnabled
  };
};