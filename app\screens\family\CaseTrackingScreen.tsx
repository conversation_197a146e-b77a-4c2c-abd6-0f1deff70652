import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User } from '../../services/authService';
import { useTranslation } from 'react-i18next';

interface CaseTrackingScreenProps {
  user: User;
  onBack: () => void;
}

const CaseTrackingScreen = ({ user, onBack }: CaseTrackingScreenProps) => {
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState<'timeline' | 'details'>('timeline');

  // Mock data - gerçek uygulamada API'den gelecek
  const caseInfo = {
    id: '1',
    deceased_name: '<PERSON><PERSON> Yılmaz',
    case_number: 'DITIB-2024-001',
    status: 'IN_PROGRESS',
    progress: 65,
    created_at: '2024-01-20T09:00:00Z',
    estimated_completion: '2024-01-22T16:00:00Z',
    assigned_driver: '<PERSON>',
    driver_phone: '+90 532 123 45 67',
    family_contact: '<PERSON><PERSON><PERSON>',
    family_phone: '+90 533 987 65 43',
    location: 'Fatih, İstanbul',
    mosque: 'Fatih Camii',
    cemetery: 'Karacaahmet Mezarlığı',
  };

  const processSteps = [
    {
      id: 1,
      title: 'Başvuru Alındı',
      description: 'Cenaze süreci başlatıldı ve sistem kaydı oluşturuldu',
      status: 'COMPLETED',
      date: '2024-01-20T09:00:00Z',
      details: 'Vaka numarası: DITIB-2024-001',
      responsible: 'Sistem',
    },
    {
      id: 2,
      title: 'Sürücü Atandı',
      description: 'Ali Kaya görevlendirildi ve bilgilendirildi',
      status: 'COMPLETED',
      date: '2024-01-20T09:30:00Z',
      details: 'Telefon: +90 532 123 45 67',
      responsible: 'Ali Kaya',
    },
    {
      id: 3,
      title: 'Hastane İşlemleri',
      description: 'Gerekli belgeler hastaneden alındı',
      status: 'COMPLETED',
      date: '2024-01-20T11:00:00Z',
      details: 'Ölüm belgesi ve diğer evraklar tamamlandı',
      responsible: 'Ali Kaya',
    },
    {
      id: 4,
      title: 'Belge Teslimi',
      description: 'Nüfus müdürlüğüne belge teslimi yapılıyor',
      status: 'IN_PROGRESS',
      date: '2024-01-20T14:00:00Z',
      details: 'Üsküdar Nüfus Müdürlüğü',
      responsible: 'Ali Kaya',
    },
    {
      id: 5,
      title: 'Cami Koordinasyonu',
      description: 'Cenaze töreni için cami ile görüşme',
      status: 'PENDING',
      date: null,
      details: 'Fatih Camii - Tören saati planlanacak',
      responsible: 'Ali Kaya',
    },
    {
      id: 6,
      title: 'Cenaze Töreni',
      description: 'Fatih Camii\'nde cenaze töreni',
      status: 'PENDING',
      date: null,
      details: 'Tahmini saat: 14:00',
      responsible: 'Ali Kaya',
    },
    {
      id: 7,
      title: 'Defin İşlemi',
      description: 'Karacaahmet Mezarlığı\'nda defin',
      status: 'PENDING',
      date: null,
      details: 'Mezar yeri hazırlanacak',
      responsible: 'Ali Kaya',
    },
    {
      id: 8,
      title: 'Süreç Tamamlandı',
      description: 'Tüm işlemler tamamlandı',
      status: 'PENDING',
      date: null,
      details: 'Final rapor hazırlanacak',
      responsible: 'Sistem',
    },
  ];

  const handleCallDriver = () => {
    Alert.alert(
      'Sürücü İletişimi',
      `${caseInfo.assigned_driver} ile iletişime geçmek istediğinizden emin misiniz?\n\n${caseInfo.driver_phone}`,
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Ara', onPress: () => console.log(`Calling ${caseInfo.driver_phone}`) },
      ]
    );
  };



  const getStepIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'checkmark-circle';
      case 'IN_PROGRESS': return 'time';
      case 'PENDING': return 'ellipse-outline';
      default: return 'ellipse-outline';
    }
  };

  const getStepColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return '#2ECC71';
      case 'IN_PROGRESS': return '#F39C12';
      case 'PENDING': return '#E1E8ED';
      default: return '#E1E8ED';
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Bekliyor';
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const completedSteps = processSteps.filter(step => step.status === 'COMPLETED').length;
  const totalSteps = processSteps.length;
  const progressPercentage = Math.round((completedSteps / totalSteps) * 100);

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#9B59B6" />
        </TouchableOpacity>
        <Text style={styles.title}>Vaka Takibi</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Case Summary */}
      <View style={styles.summarySection}>
        <View style={styles.summaryCard}>
          <View style={styles.summaryHeader}>
            <View style={styles.summaryInfo}>
              <Text style={styles.deceasedName}>{caseInfo.deceased_name}</Text>
              <Text style={styles.caseNumber}>Vaka No: {caseInfo.case_number}</Text>
            </View>
            <View style={styles.progressCircle}>
              <Text style={styles.progressText}>{progressPercentage}%</Text>
            </View>
          </View>

          <View style={styles.progressSection}>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: `${progressPercentage}%` }]} />
            </View>
            <Text style={styles.progressLabel}>
              {completedSteps}/{totalSteps} adım tamamlandı
            </Text>
          </View>

          <TouchableOpacity style={styles.callDriverButton} onPress={handleCallDriver}>
            <Ionicons name="call" size={16} color="#9B59B6" />
            <Text style={styles.callDriverText}>
              {caseInfo.assigned_driver} ile İletişim
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Tabs */}
      <View style={styles.tabSection}>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'timeline' && styles.activeTab]}
          onPress={() => setSelectedTab('timeline')}
        >
          <Text style={[styles.tabText, selectedTab === 'timeline' && styles.activeTabText]}>
            Süreç Takibi
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'details' && styles.activeTab]}
          onPress={() => setSelectedTab('details')}
        >
          <Text style={[styles.tabText, selectedTab === 'details' && styles.activeTabText]}>
            Detaylar
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {selectedTab === 'timeline' ? (
          <View style={styles.timelineContainer}>
            {processSteps.map((step, index) => (
              <View key={step.id} style={styles.timelineItem}>
                <View style={styles.timelineIconContainer}>
                  <View style={[
                    styles.timelineIcon,
                    { backgroundColor: getStepColor(step.status) }
                  ]}>
                    <Ionicons
                      name={getStepIcon(step.status) as any}
                      size={16}
                      color={step.status === 'PENDING' ? '#8E9297' : '#FFFFFF'}
                    />
                  </View>
                  {index < processSteps.length - 1 && (
                    <View style={[
                      styles.timelineLine,
                      { backgroundColor: step.status === 'COMPLETED' ? '#2ECC71' : '#E1E8ED' }
                    ]} />
                  )}
                </View>
                <View style={styles.timelineContent}>
                  <View style={styles.timelineHeader}>
                    <Text style={styles.timelineTitle}>{step.title}</Text>
                    <View style={[
                      styles.statusBadge,
                      { backgroundColor: getStepColor(step.status) }
                    ]}>
                      <Text style={[
                        styles.statusText,
                        { color: step.status === 'PENDING' ? '#8E9297' : '#FFFFFF' }
                      ]}>
                        {step.status === 'COMPLETED' ? 'Tamamlandı' :
                         step.status === 'IN_PROGRESS' ? 'Devam Ediyor' : 'Bekliyor'}
                      </Text>
                    </View>
                  </View>
                  <Text style={styles.timelineDescription}>{step.description}</Text>
                  <Text style={styles.timelineDetails}>{step.details}</Text>
                  <View style={styles.timelineFooter}>
                    <Text style={styles.timelineDate}>{formatDate(step.date)}</Text>
                    <Text style={styles.timelineResponsible}>Sorumlu: {step.responsible}</Text>
                  </View>
                </View>
              </View>
            ))}
          </View>
        ) : (
          <View style={styles.detailsContainer}>
            <View style={styles.detailSection}>
              <Text style={styles.detailSectionTitle}>Vaka Bilgileri</Text>
              <View style={styles.detailCard}>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Vaka Numarası:</Text>
                  <Text style={styles.detailValue}>{caseInfo.case_number}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Başlangıç Tarihi:</Text>
                  <Text style={styles.detailValue}>{formatDate(caseInfo.created_at)}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Tahmini Bitiş:</Text>
                  <Text style={styles.detailValue}>{formatDate(caseInfo.estimated_completion)}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Durum:</Text>
                  <Text style={styles.detailValue}>Devam Ediyor</Text>
                </View>
              </View>
            </View>

            <View style={styles.detailSection}>
              <Text style={styles.detailSectionTitle}>İletişim Bilgileri</Text>
              <View style={styles.detailCard}>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Sorumlu Sürücü:</Text>
                  <Text style={styles.detailValue}>{caseInfo.assigned_driver}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Sürücü Telefon:</Text>
                  <Text style={styles.detailValue}>{caseInfo.driver_phone}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Aile İletişim:</Text>
                  <Text style={styles.detailValue}>{caseInfo.family_contact}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Aile Telefon:</Text>
                  <Text style={styles.detailValue}>{caseInfo.family_phone}</Text>
                </View>
              </View>
            </View>

            <View style={styles.detailSection}>
              <Text style={styles.detailSectionTitle}>Lokasyon Bilgileri</Text>
              <View style={styles.detailCard}>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Bölge:</Text>
                  <Text style={styles.detailValue}>{caseInfo.location}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Cami:</Text>
                  <Text style={styles.detailValue}>{caseInfo.mosque}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Mezarlık:</Text>
                  <Text style={styles.detailValue}>{caseInfo.cemetery}</Text>
                </View>
              </View>
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  headerRight: {
    width: 40,
    height: 40,
  },
  summarySection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  summaryCard: {
    backgroundColor: '#F5F8FA',
    borderRadius: 16,
    padding: 14,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  summaryInfo: {
    flex: 1,
  },
  deceasedName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  caseNumber: {
    fontSize: 12,
    color: '#8E9297',
  },
  progressCircle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#9B59B6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressText: {
    fontSize: 14,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  progressSection: {
    marginBottom: 12,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E1E8ED',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#9B59B6',
    borderRadius: 4,
  },
  progressLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#9B59B6',
    textAlign: 'center',
  },
  callDriverButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#9B59B6',
  },
  callDriverText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#9B59B6',
    marginLeft: 8,
  },
  tabSection: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#9B59B6',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#8E9297',
  },
  activeTabText: {
    color: '#9B59B6',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  timelineContainer: {
    paddingTop: 24,
    paddingBottom: 32,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  timelineIconContainer: {
    alignItems: 'center',
    marginRight: 16,
  },
  timelineIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timelineLine: {
    width: 2,
    height: 32,
    marginTop: 4,
  },
  timelineContent: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  timelineHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  timelineTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  timelineDescription: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 8,
  },
  timelineDetails: {
    fontSize: 12,
    color: '#9B59B6',
    marginBottom: 12,
  },
  timelineFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timelineDate: {
    fontSize: 12,
    color: '#B8C5D1',
  },
  timelineResponsible: {
    fontSize: 12,
    color: '#B8C5D1',
  },
  detailsContainer: {
    paddingTop: 24,
    paddingBottom: 32,
  },
  detailSection: {
    marginBottom: 24,
  },
  detailSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 12,
  },
  detailCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F8FA',
  },
  detailLabel: {
    fontSize: 14,
    color: '#8E9297',
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
    flex: 1,
    textAlign: 'right',
  },
});

export default CaseTrackingScreen;
