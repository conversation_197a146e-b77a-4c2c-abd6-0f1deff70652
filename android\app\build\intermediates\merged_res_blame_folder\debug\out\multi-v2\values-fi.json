{"logs": [{"outputFile": "com.ditib.funeralapp-mergeDebugResources-36:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dbbbc453ac84f8e1f55ee9b7498bc2\\transformed\\appcompat-1.6.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,9801", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,9877"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\97dafb02ce1c76f135d92386cd4ea378\\transformed\\core-1.12.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "39,40,41,42,43,44,45,126", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3463,3559,3661,3759,3864,3969,4081,10530", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3554,3656,3754,3859,3964,4076,4192,10626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\043814979a7351adeafae334529a11be\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,239,328,410,482,550,633,702,772,851,930,1015,1101,1175,1257,1341,1417,1502,1586,1666,1745,1820,1905,1981,2061,2132", "endColumns": "70,112,88,81,71,67,82,68,69,78,78,84,85,73,81,83,75,84,83,79,78,74,84,75,79,70,78", "endOffsets": "121,234,323,405,477,545,628,697,767,846,925,1010,1096,1170,1252,1336,1412,1497,1581,1661,1740,1815,1900,1976,2056,2127,2206"}, "to": {"startLines": "33,46,47,51,54,56,57,59,73,74,75,113,114,115,116,118,119,120,121,122,123,124,125,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2988,4197,4310,4687,4927,5074,5142,5290,6304,6374,6453,9474,9559,9645,9719,9882,9966,10042,10127,10211,10291,10370,10445,10631,10707,10787,10858", "endColumns": "70,112,88,81,71,67,82,68,69,78,78,84,85,73,81,83,75,84,83,79,78,74,84,75,79,70,78", "endOffsets": "3054,4305,4394,4764,4994,5137,5220,5354,6369,6448,6527,9554,9640,9714,9796,9961,10037,10122,10206,10286,10365,10440,10525,10702,10782,10853,10932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\284bdced76504d9ec0db1a02932d1b74\\transformed\\material-1.9.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1025,1118,1193,1258,1346,1411,1477,1535,1606,1672,1726,1836,1896,1960,2014,2087,2203,2287,2368,2501,2586,2671,2761,2813,2864,2930,3007,3089,3173,3247,3326,3403,3475,3564,3640,3731,3826,3900,3973,4067,4121,4193,4279,4365,4427,4491,4554,4655,4757,4852,4955,5011,5066", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,89,51,50,65,76,81,83,73,78,76,71,88,75,90,94,73,72,93,53,71,85,85,61,63,62,100,101,94,102,55,54,78", "endOffsets": "263,339,413,496,585,667,763,871,955,1020,1113,1188,1253,1341,1406,1472,1530,1601,1667,1721,1831,1891,1955,2009,2082,2198,2282,2363,2496,2581,2666,2756,2808,2859,2925,3002,3084,3168,3242,3321,3398,3470,3559,3635,3726,3821,3895,3968,4062,4116,4188,4274,4360,4422,4486,4549,4650,4752,4847,4950,5006,5061,5140"}, "to": {"startLines": "2,34,35,36,37,38,48,49,50,52,53,55,58,60,61,62,63,64,65,66,67,68,69,70,71,72,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3059,3135,3209,3292,3381,4399,4495,4603,4769,4834,4999,5225,5359,5447,5512,5578,5636,5707,5773,5827,5937,5997,6061,6115,6188,6532,6616,6697,6830,6915,7000,7090,7142,7193,7259,7336,7418,7502,7576,7655,7732,7804,7893,7969,8060,8155,8229,8302,8396,8450,8522,8608,8694,8756,8820,8883,8984,9086,9181,9284,9340,9395", "endLines": "5,34,35,36,37,38,48,49,50,52,53,55,58,60,61,62,63,64,65,66,67,68,69,70,71,72,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,89,51,50,65,76,81,83,73,78,76,71,88,75,90,94,73,72,93,53,71,85,85,61,63,62,100,101,94,102,55,54,78", "endOffsets": "313,3130,3204,3287,3376,3458,4490,4598,4682,4829,4922,5069,5285,5442,5507,5573,5631,5702,5768,5822,5932,5992,6056,6110,6183,6299,6611,6692,6825,6910,6995,7085,7137,7188,7254,7331,7413,7497,7571,7650,7727,7799,7888,7964,8055,8150,8224,8297,8391,8445,8517,8603,8689,8751,8815,8878,8979,9081,9176,9279,9335,9390,9469"}}]}]}