{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": false, "noImplicitAny": false, "noUnusedParameters": false, "skipLibCheck": true, "allowJs": true, "noUnusedLocals": false, "strictNullChecks": false, "target": "ES2017", "lib": ["ES2017", "DOM"], "module": "ESNext", "moduleResolution": "bundler", "jsx": "react-native", "baseUrl": ".", "paths": {"@/*": ["./app/*"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["web-backup/**/*"]}