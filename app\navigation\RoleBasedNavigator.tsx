import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { User } from '../services/authService';
import { useUserActivity } from '../hooks/useUserActivity';

// Import role-specific navigators (will create these next)
import AdminNavigator from './AdminNavigator';
import DriverNavigator from './DriverNavigator';
import FamilyNavigator from './FamilyNavigator';

interface RoleBasedNavigatorProps {
  user: User;
  onLogout: () => void;
}

const RoleBasedNavigator = ({ user, onLogout }: RoleBasedNavigatorProps) => {
  console.log('RoleBasedNavigator: User role:', user.role);

  // User activity tracking
  const { panResponder } = useUserActivity();

  // Route based on user role
  switch (user.role) {
    case 'ADMIN':
      return (
        <View style={{ flex: 1 }} {...panResponder.panHandlers}>
          <AdminNavigator user={user} onLogout={onLogout} />
        </View>
      );

    case 'DRIVER':
      return (
        <View style={{ flex: 1 }} {...panResponder.panHandlers}>
          <DriverNavigator user={user} onLogout={onLogout} />
        </View>
      );

    case 'FAMILY':
      return (
        <View style={{ flex: 1 }} {...panResponder.panHandlers}>
          <FamilyNavigator user={user} onLogout={onLogout} />
        </View>
      );

    default:
      // Fallback for unknown roles - show error screen
      return (
        <View style={styles.errorContainer} {...panResponder.panHandlers}>
          <Text style={styles.errorTitle}>Bilinmeyen Rol</Text>
          <Text style={styles.errorMessage}>
            Kullanıcı rolü tanımlanamadı: {user.role}
          </Text>
          <Text style={styles.errorSubMessage}>
            Lütfen yöneticinizle iletişime geçin.
          </Text>
        </View>
      );
  }
};

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F8FA',
    paddingHorizontal: 24,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#E74C3C',
    marginBottom: 16,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#1C1C1E',
    marginBottom: 8,
    textAlign: 'center',
  },
  errorSubMessage: {
    fontSize: 14,
    color: '#8E9297',
    textAlign: 'center',
  },
});

export default RoleBasedNavigator;
