import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User } from '../../services/authService';
import { useTranslation } from 'react-i18next';
import { useDocumentAlerts } from '../../hooks/useDocumentAlerts';

interface FamilyDocumentsScreenProps {
  user: User;
  onBack: () => void;
}

const FamilyDocumentsScreen = ({ user, onBack }: FamilyDocumentsScreenProps) => {
  const { t } = useTranslation();
  const { showDocumentRequestAlert } = useDocumentAlerts();
  const [selectedFilter, setSelectedFilter] = useState<'ALL' | 'OFFICIAL' | 'PHOTOS' | 'CERTIFICATES'>('ALL');

  // Mock data - gerçek uygulamada API'den gelecek
  const documents = [
    {
      id: '1',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      type: 'OFFICIAL',
      description: '<PERSON>smi ö<PERSON>üm belge<PERSON> - <PERSON>ü<PERSON> onaylı',
      case_name: 'Ahmet Yılmaz Vakası',
      uploaded_at: '2024-01-20T11:30:00Z',
      file_size: '1.2 MB',
      status: 'APPROVED',
      is_downloadable: true,
      pages: 2,
    },
    {
      id: '2',
      name: 'Defin Ruhsatı',
      type: 'OFFICIAL',
      description: 'Mezarlık defin izin belgesi',
      case_name: 'Ahmet Yılmaz Vakası',
      uploaded_at: '2024-01-20T14:15:00Z',
      file_size: '0.8 MB',
      status: 'APPROVED',
      is_downloadable: true,
      pages: 1,
    },
    {
      id: '3',
      name: 'Cenaze Töreni Fotoğrafları',
      type: 'PHOTOS',
      description: 'Fatih Camii cenaze töreni fotoğrafları',
      case_name: 'Ahmet Yılmaz Vakası',
      uploaded_at: '2024-01-21T16:45:00Z',
      file_size: '8.4 MB',
      status: 'APPROVED',
      is_downloadable: true,
      pages: 12,
    },
    {
      id: '4',
      name: 'Hastane Çıkış Belgesi',
      type: 'OFFICIAL',
      description: 'Hastane morgue çıkış belgesi',
      case_name: 'Ahmet Yılmaz Vakası',
      uploaded_at: '2024-01-20T10:20:00Z',
      file_size: '0.6 MB',
      status: 'APPROVED',
      is_downloadable: true,
      pages: 1,
    },
    {
      id: '5',
      name: 'Tamamlama Sertifikası',
      type: 'CERTIFICATES',
      description: 'Cenaze süreci tamamlama sertifikası',
      case_name: 'Ahmet Yılmaz Vakası',
      uploaded_at: '2024-01-22T17:00:00Z',
      file_size: '1.1 MB',
      status: 'PENDING',
      is_downloadable: false,
      pages: 1,
    },
  ];

  const typeColors = {
    OFFICIAL: '#0D3B66',
    PHOTOS: '#9B59B6',
    CERTIFICATES: '#2ECC71',
  };

  const typeLabels = {
    OFFICIAL: 'Resmi Belge',
    PHOTOS: 'Fotoğraflar',
    CERTIFICATES: 'Sertifikalar',
  };

  const statusColors = {
    APPROVED: '#2ECC71',
    PENDING: '#F39C12',
    PROCESSING: '#9B59B6',
  };

  const statusLabels = {
    APPROVED: 'Hazır',
    PENDING: 'Hazırlanıyor',
    PROCESSING: 'İşleniyor',
  };

  const filteredDocuments = documents.filter(doc => 
    selectedFilter === 'ALL' || doc.type === selectedFilter
  );

  const handleViewDocument = (documentId: string, documentName: string) => {
    Alert.alert(t('alerts.documentView'), `"${documentName}" ${t('alerts.documentViewing')}`);
  };

  const handleDownloadDocument = (documentId: string, documentName: string) => {
    Alert.alert(
      'Belge İndir',
      `"${documentName}" belgesini indirmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'İndir', onPress: () => console.log(`Downloading document ${documentId}`) },
      ]
    );
  };

  const handleShareDocument = (documentId: string, documentName: string) => {
    Alert.alert(
      'Belge Paylaş',
      `"${documentName}" belgesini paylaşmak istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Paylaş', onPress: () => console.log(`Sharing document ${documentId}`) },
      ]
    );
  };

  const handleRequestDocument = () => {
    showDocumentRequestAlert('family', {
      onAdditionalCopy: () => console.log('Requesting additional copy'),
      onSpecialDocument: () => console.log('Requesting special document'),
      onCancel: () => console.log('Request cancelled')
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDocumentStats = () => {
    const ready = documents.filter(d => d.status === 'APPROVED').length;
    const preparing = documents.filter(d => d.status === 'PENDING' || d.status === 'PROCESSING').length;
    const totalSize = documents.reduce((sum, d) => {
      const size = parseFloat(d.file_size.replace(' MB', ''));
      return sum + size;
    }, 0);
    
    return { ready, preparing, totalSize: totalSize.toFixed(1) };
  };

  const stats = getDocumentStats();

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#9B59B6" />
        </TouchableOpacity>
        <Text style={styles.title}>Belgeler</Text>
        <TouchableOpacity onPress={handleRequestDocument} style={styles.requestButton}>
          <Ionicons name="add-circle" size={24} color="#9B59B6" />
        </TouchableOpacity>
      </View>

      {/* Stats */}
      <View style={styles.statsSection}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.ready}</Text>
          <Text style={styles.statLabel}>Hazır</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.preparing}</Text>
          <Text style={styles.statLabel}>Hazırlanıyor</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.totalSize} MB</Text>
          <Text style={styles.statLabel}>Toplam Boyut</Text>
        </View>
      </View>

      {/* Filter */}
      <View style={styles.filterSection}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {(['ALL', 'OFFICIAL', 'PHOTOS', 'CERTIFICATES'] as const).map((filter) => (
            <TouchableOpacity
              key={filter}
              style={[
                styles.filterButton,
                selectedFilter === filter && styles.filterButtonActive
              ]}
              onPress={() => setSelectedFilter(filter)}
            >
              <Text style={[
                styles.filterButtonText,
                selectedFilter === filter && styles.filterButtonTextActive
              ]}>
                {filter === 'ALL' ? 'Tümü' : typeLabels[filter]}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Documents List */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {filteredDocuments.map((document) => (
          <TouchableOpacity
            key={document.id}
            style={styles.documentCard}
            onPress={() => handleViewDocument(document.id, document.name)}
          >
            <View style={styles.documentHeader}>
              <View style={styles.documentIcon}>
                <Ionicons 
                  name={
                    document.type === 'PHOTOS' ? 'images' :
                    document.type === 'CERTIFICATES' ? 'ribbon' : 'document-text'
                  } 
                  size={24} 
                  color={typeColors[document.type as keyof typeof typeColors]} 
                />
              </View>
              <View style={styles.documentInfo}>
                <Text style={styles.documentName}>{document.name}</Text>
                <Text style={styles.documentDescription}>{document.description}</Text>
                <View style={styles.documentMeta}>
                  <View style={[
                    styles.typeBadge,
                    { backgroundColor: typeColors[document.type as keyof typeof typeColors] }
                  ]}>
                    <Text style={styles.typeText}>
                      {typeLabels[document.type as keyof typeof typeLabels]}
                    </Text>
                  </View>
                  <View style={[
                    styles.statusBadge,
                    { backgroundColor: statusColors[document.status as keyof typeof statusColors] }
                  ]}>
                    <Text style={styles.statusText}>
                      {statusLabels[document.status as keyof typeof statusLabels]}
                    </Text>
                  </View>
                </View>
              </View>
              {document.is_downloadable && (
                <View style={styles.downloadIndicator}>
                  <Ionicons name="download" size={20} color="#2ECC71" />
                </View>
              )}
            </View>

            {/* Document Details */}
            <View style={styles.documentDetails}>
              <View style={styles.detailRow}>
                <View style={styles.detailItem}>
                  <Ionicons name="time" size={14} color="#8E9297" />
                  <Text style={styles.detailText}>
                    {formatDate(document.uploaded_at)}
                  </Text>
                </View>
                <View style={styles.detailItem}>
                  <Ionicons name="folder" size={14} color="#8E9297" />
                  <Text style={styles.detailText}>{document.file_size}</Text>
                </View>
              </View>
              
              <View style={styles.detailRow}>
                <View style={styles.detailItem}>
                  <Ionicons name="document" size={14} color="#8E9297" />
                  <Text style={styles.detailText}>
                    {document.pages} {document.type === 'PHOTOS' ? 'fotoğraf' : 'sayfa'}
                  </Text>
                </View>
              </View>

              {/* Action Buttons */}
              {document.is_downloadable && (
                <View style={styles.actionButtons}>
                  <TouchableOpacity
                    style={styles.viewButton}
                    onPress={() => handleViewDocument(document.id, document.name)}
                  >
                    <Ionicons name="eye" size={16} color="#9B59B6" />
                    <Text style={styles.viewButtonText}>Görüntüle</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={styles.downloadButton}
                    onPress={() => handleDownloadDocument(document.id, document.name)}
                  >
                    <Ionicons name="download" size={16} color="#2ECC71" />
                    <Text style={styles.downloadButtonText}>İndir</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={styles.shareButton}
                    onPress={() => handleShareDocument(document.id, document.name)}
                  >
                    <Ionicons name="share" size={16} color="#F39C12" />
                    <Text style={styles.shareButtonText}>Paylaş</Text>
                  </TouchableOpacity>
                </View>
              )}

              {!document.is_downloadable && (
                <View style={styles.preparingInfo}>
                  <Ionicons name="time" size={16} color="#F39C12" />
                  <Text style={styles.preparingText}>
                    Belge hazırlanıyor, hazır olduğunda bildirim alacaksınız.
                  </Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        ))}

        {filteredDocuments.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="document-outline" size={64} color="#E1E8ED" />
            <Text style={styles.emptyStateTitle}>Belge bulunamadı</Text>
            <Text style={styles.emptyStateText}>
              {selectedFilter === 'ALL' 
                ? 'Henüz hazır belge bulunmuyor.'
                : `${typeLabels[selectedFilter]} kategorisinde belge bulunmuyor.`
              }
            </Text>
          </View>
        )}

        {/* Info Card */}
        <View style={styles.infoCard}>
          <View style={styles.infoHeader}>
            <Ionicons name="information-circle" size={20} color="#9B59B6" />
            <Text style={styles.infoTitle}>Belge Bilgilendirmesi</Text>
          </View>
          <Text style={styles.infoText}>
            • Tüm belgeler dijital olarak saklanır ve güvenlidir{'\n'}
            • İndirilen belgeler cihazınızda saklanır{'\n'}
            • Ek belge talepleriniz için + butonunu kullanın{'\n'}
            • Sorularınız için 7/24 destek hattımızı arayın
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  requestButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsSection: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 16,
    fontWeight: '700',
    color: '#9B59B6',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 10,
    color: '#8E9297',
  },
  filterSection: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: '#9B59B6',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8E9297',
  },
  filterButtonTextActive: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  documentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  documentHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  documentIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  documentDescription: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 8,
  },
  documentMeta: {
    flexDirection: 'row',
    gap: 8,
  },
  typeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  typeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  downloadIndicator: {
    marginLeft: 8,
  },
  documentDetails: {
    borderTopWidth: 1,
    borderTopColor: '#F5F8FA',
    paddingTop: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  detailText: {
    fontSize: 12,
    color: '#8E9297',
    marginLeft: 6,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 12,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F8FA',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flex: 1,
    borderWidth: 1,
    borderColor: '#9B59B6',
  },
  viewButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#9B59B6',
    marginLeft: 4,
  },
  downloadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#2ECC71',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flex: 1,
  },
  downloadButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 4,
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F39C12',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flex: 1,
  },
  shareButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 4,
  },
  preparingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF9E6',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
  },
  preparingText: {
    fontSize: 12,
    color: '#8B6914',
    marginLeft: 8,
    flex: 1,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#8E9297',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#B8C5D1',
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  infoCard: {
    backgroundColor: '#F0F9FF',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    marginBottom: 32,
    borderWidth: 1,
    borderColor: '#9B59B6',
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4A148C',
    marginLeft: 8,
  },
  infoText: {
    fontSize: 12,
    color: '#4A148C',
    lineHeight: 18,
  },
});

export default FamilyDocumentsScreen;
