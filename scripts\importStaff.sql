-- DITIB Personel Import Script
-- Mevcut personelleri staff tablosuna aktarmak için

-- <PERSON><PERSON><PERSON> personel verileri (gerçek verilerle değiş<PERSON>rin)
INSERT INTO public.staff (id, email, full_name, role, phone, created_at, updated_at) VALUES
-- <PERSON><PERSON> person<PERSON>
(gen_random_uuid(), 'ahmet.yil<PERSON><EMAIL>', '<PERSON><PERSON> Yılmaz', 'ADMIN', '+90 555 123 45 67', NOW(), NOW()),
(gen_random_uuid(), '<EMAIL>', '<PERSON><PERSON> Özkan', 'ADMIN', '+90 555 234 56 78', NOW(), NOW()),

-- <PERSON><PERSON><PERSON><PERSON>
(gen_random_uuid(), '<EMAIL>', '<PERSON><PERSON><PERSON>', 'IMAM', '+90 555 345 67 89', NOW(), NOW()),
(gen_random_uuid(), '<EMAIL>', '<PERSON>', 'IMAM', '+90 555 456 78 90', NOW(), NOW()),
(gen_random_uuid(), '<EMAIL>', '<PERSON>', '<PERSON><PERSON><PERSON>', '+90 555 567 89 01', NOW(), NOW()),

-- Şoförler
(gen_random_uuid(), '<EMAIL>', 'Ayşe Şahin', 'DRIVER', '+90 555 678 90 12', NOW(), NOW()),
(gen_random_uuid(), '<EMAIL>', 'Zeynep Arslan', 'LOGISTICS', '+90 555 789 01 23', NOW(), NOW()),

-- Muhasebe personeli (Admin olarak)
(gen_random_uuid(), '<EMAIL>', 'İbrahim Koç', 'ADMIN', '+90 555 890 12 34', NOW(), NOW()),
(gen_random_uuid(), '<EMAIL>', 'Meryem Yıldız', 'ADMIN', '+90 555 901 23 45', NOW(), NOW()),

-- Teknik personel (Admin olarak)
(gen_random_uuid(), '<EMAIL>', 'Mustafa Özdemir', 'ADMIN', '+90 555 012 34 56', NOW(), NOW());

-- Kontrol sorgusu
SELECT
  email,
  full_name,
  role,
  phone,
  created_at
FROM public.staff
ORDER BY created_at DESC;

-- İstatistik
SELECT
  role,
  COUNT(*) as personel_sayisi
FROM public.staff
GROUP BY role
ORDER BY role;
