import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from '../i18n/useTranslation';

interface LanguageScreenProps {
  onBack: () => void;
}

const LanguageScreen = ({ onBack }: LanguageScreenProps) => {
  const { t, changeLanguage, getCurrentLanguage } = useTranslation();
  const [selectedLanguage, setSelectedLanguage] = useState('tr');

  useEffect(() => {
    setSelectedLanguage(getCurrentLanguage());
  }, []);

  const languages = [
    {
      code: 'tr',
      name: 'Türkçe',
      flag: '🇹🇷',
      nativeName: 'Türkçe',
    },
    {
      code: 'en',
      name: 'English',
      flag: '🇺🇸',
      nativeName: 'English',
    },
    {
      code: 'de',
      name: '<PERSON><PERSON><PERSON>',
      flag: '🇩🇪',
      nativeName: 'Deutsch',
    },
  ];

  const handleLanguageSelect = async (languageCode: string) => {
    setSelectedLanguage(languageCode);
    await changeLanguage(languageCode);
    console.log('Language changed to:', languageCode);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#1C1C1E" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{t('language.selectLanguage')}</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Content */}
      <View style={styles.content}>
        <Text style={styles.sectionTitle}>{t('settings.language')}</Text>
        <Text style={styles.sectionDescription}>
          {t('language.description')}
        </Text>

        <View style={styles.languageList}>
          {languages.map((language) => (
            <TouchableOpacity
              key={language.code}
              style={[
                styles.languageItem,
                selectedLanguage === language.code && styles.languageItemSelected
              ]}
              onPress={() => handleLanguageSelect(language.code)}
            >
              <View style={styles.languageLeft}>
                <Text style={styles.languageFlag}>{language.flag}</Text>
                <View style={styles.languageInfo}>
                  <Text style={[
                    styles.languageName,
                    selectedLanguage === language.code && styles.languageNameSelected
                  ]}>
                    {language.name}
                  </Text>
                  <Text style={[
                    styles.languageNative,
                    selectedLanguage === language.code && styles.languageNativeSelected
                  ]}>
                    {language.nativeName}
                  </Text>
                </View>
              </View>

              {selectedLanguage === language.code && (
                <View style={styles.selectedIndicator}>
                  <Ionicons name="checkmark-circle" size={24} color="#FAA916" />
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>

        {/* Info Section */}
        <View style={styles.infoSection}>
          <View style={styles.infoCard}>
            <Ionicons name="information-circle" size={20} color="#FAA916" />
            <Text style={styles.infoText}>
              Dil değişikliği tüm menüler, butonlar ve sistem mesajları için geçerli olacaktır.
            </Text>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#8E9297',
    lineHeight: 20,
    marginBottom: 32,
  },
  languageList: {
    gap: 12,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: '#E1E8ED',
  },
  languageItemSelected: {
    borderColor: '#FAA916',
    backgroundColor: '#FFF9F0',
  },
  languageLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  languageFlag: {
    fontSize: 24,
    marginRight: 16,
  },
  languageInfo: {
    flex: 1,
  },
  languageName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  languageNameSelected: {
    color: '#FAA916',
  },
  languageNative: {
    fontSize: 14,
    color: '#8E9297',
  },
  languageNativeSelected: {
    color: '#FAA916',
  },
  selectedIndicator: {
    marginLeft: 12,
  },
  infoSection: {
    marginTop: 32,
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#FFF9F0',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#FAA916',
  },
  infoText: {
    fontSize: 14,
    color: '#8E9297',
    lineHeight: 20,
    marginLeft: 12,
    flex: 1,
  },
});

export default LanguageScreen;
